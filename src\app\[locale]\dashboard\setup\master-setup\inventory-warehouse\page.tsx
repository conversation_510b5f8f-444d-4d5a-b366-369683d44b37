import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import WarehouseQuery from '@/services/queries/WarehouseQuery';
import InventoryWarehouseTable from './components/inventory_warehouse_table';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Goodkey | Inventory Warehouse',
};

export default async function AdvanceReceivingWarehouse() {
  const queryClient = getQueryClient();

  await queryClient.prefetchQuery({
    queryKey: [WarehouseQuery.tags, { warehouseType: 2 }],
    queryFn: () => WarehouseQuery.getAll(2),
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        { title: 'Master-Setup', link: '/dashboard/setup/master-setup' },
        {
          title: 'Inventory Warehouse',
          link: '/dashboard/setup/master-setup/inventory-warehouse',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <InventoryWarehouseTable />
      </HydrationBoundary>
    </AppLayout>
  );
}
