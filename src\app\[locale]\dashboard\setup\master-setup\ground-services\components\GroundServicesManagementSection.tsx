'use client';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import GroundServiceQuery from '@/services/queries/GroundServiceQuery';
import { GroundService } from '@/models/GroundService';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import GroundServiceModal from './GroundServiceModal';
import { useMemo } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';
import { CheckCircleIcon, XCircleIcon } from '@/assets/Icons';
import Link from 'next/link';

export default function GroundServicesManagementSection() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data, isLoading } = useQuery({
    queryKey: ['ground-services'],
    queryFn: GroundServiceQuery.getAll,
  });

  const deleteMutation = useMutation({
    mutationFn: GroundServiceQuery.delete,
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Ground service deleted successfully',
        variant: 'success',
      });
      queryClient.invalidateQueries({ queryKey: ['ground-services'] });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete ground service',
        variant: 'destructive',
      });
    },
  });

  const handleAdd = () => {
    modal(<GroundServiceModal />, { ...DEFAULT_MODAL, width: '75%' }).open();
  };
  const handleEdit = (service: GroundService) => {
    modal(<GroundServiceModal serviceId={service.id} />, {
      ...DEFAULT_MODAL,
      width: '75%',
    }).open();
  };

  const columns = useMemo(
    () =>
      generateTableColumns<GroundService>(
        {
          name: { name: 'Name', type: 'text', sortable: true },
          description: { name: 'Description', type: 'text' },
          conditions: { name: 'Conditions', type: 'text' },
          flatRate: { name: 'Flat Rate', type: 'text' },
          localCartageAppliable: {
            name: 'Local Cartage',
            type: {
              type: 'node',
              render: ({ cell }) => (
                <div className="flex items-center whitespace-nowrap">
                  {cell ? (
                    <>
                      <CheckCircleIcon className="text-green-600 w-4 h-4 mr-1" />
                      <span className="text-green-600 hidden">Yes</span>
                    </>
                  ) : (
                    <>
                      <XCircleIcon className="text-red-600 w-4 h-4 mr-1" />
                      <span className="text-red-600 hidden">No</span>
                    </>
                  )}
                </div>
              ),
            },
          },
        },
        {
          action: {
            name: 'Actions',
            type: {
              type: 'node',
              render: ({ row }) => (
                <div className="flex gap-2">
                  <Link
                    href={`/dashboard/setup/master-setup/ground-services/${row.id ?? 'add'}`}
                  >
                    <Button
                      size="sm"
                      variant="secondary"
                      iconName="EditIcon"
                    ></Button>
                  </Link>
                  <Button
                    variant="remove"
                    size="sm"
                    iconName="RemoveIcon"
                    onClick={() =>
                      modal(
                        ({ close }) => (
                          <MutationConfirmModal
                            mutateFn={async () =>
                              GroundServiceQuery.delete(row.id)
                            }
                            mutationKey={['ground-services']}
                            title="Delete Ground Service"
                            description={`Are you sure you want to delete ${row.name}?`}
                            variant="destructive"
                            confirmButtonText="Delete"
                            confirmIconName="DeleteIcon"
                            loadingIconName="LoadingIcon"
                            onSuccess={() => {
                              toast({
                                title: 'Success',
                                description:
                                  'Ground service deleted successfully',
                                variant: 'success',
                              });
                              queryClient.invalidateQueries({
                                queryKey: ['ground-services'],
                              });
                            }}
                            onError={(error) => {
                              toast({
                                title: 'Error',
                                description:
                                  error.message ||
                                  'Failed to delete ground service',
                                variant: 'destructive',
                              });
                            }}
                            close={close}
                          />
                        ),
                        DEFAULT_MODAL,
                      ).open()
                    }
                    disabled={deleteMutation.isPending}
                  ></Button>
                </div>
              ),
            },
          },
        },
        false,
      ),
    [deleteMutation, queryClient, toast],
  );

  const filters = useMemo(
    () =>
      generateTableFilters<GroundService>({
        name: { name: 'Name', type: 'text' },
        description: { name: 'Description', type: 'text' },
        localCartageAppliable: {
          name: 'Local Cartage',
          type: {
            type: 'select',
            options: [
              { label: 'Yes', value: 'true' },
              { label: 'No', value: 'false' },
            ],
          },
        },
      }),
    [],
  );

  return (
    <div>
      <DataTable
        columns={columns}
        data={data}
        isLoading={isLoading}
        filterFields={filters}
        controls={
          <Link href="/dashboard/setup/master-setup/ground-services/add">
            <Button
              variant={'main'}
              iconName="AddIcon"
              iconProps={{ className: 'text-white' }}
            >
              Add Ground Service
            </Button>
          </Link>
        }
      />
    </div>
  );
}
