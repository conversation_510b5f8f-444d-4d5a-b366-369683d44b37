'use client';

import { useQuery } from '@tanstack/react-query';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import WarehouseQuery from '@/services/queries/WarehouseQuery';
import { ProvinceQuery } from '@/services/queries/ProvinceQuery';
import { WarehouseSummaryDto } from '@/models/Warehouse';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle } from 'lucide-react';
import Link from 'next/link';

export const AdvanceReceivingWarehouseTable = () => {
  // Fetch warehouses with warehouseType = 1 (Advance Receiving)
  const { data, isLoading } = useQuery({
    queryKey: [WarehouseQuery.tags, { warehouseType: 1 }],
    queryFn: () => WarehouseQuery.getAll(1),
  });

  // Fetch provinces for filter dropdown
  const { data: provinces, isLoading: isLoadingProvinces } = useQuery({
    queryKey: ProvinceQuery.tags,
    queryFn: ProvinceQuery.getAll,
  });

  const filters = generateTableFilters({
    warehouseName: {
      name: 'Name',
      type: 'text',
    },
    isActive: {
      name: 'Active',
      type: {
        type: 'select',
        options: [
          { label: 'Active', value: 'true' },
          { label: 'Inactive', value: 'false' },
        ],
      },
    },
    provinceName: {
      name: 'Province',
      type: {
        type: 'select',
        options:
          (!isLoadingProvinces &&
            provinces?.map((province) => ({
              label: province.name,
              value: province.name,
            }))) ||
          [],
      },
    },
  });

  const columns = generateTableColumns<WarehouseSummaryDto>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      code: { name: 'Code', type: 'text', sortable: true },
      warehouseName: { name: 'Warehouse Name', type: 'text', sortable: true },
      phone: { name: 'Phone', type: 'text', sortable: true },
      provinceName: { name: 'Province', type: 'text', sortable: true },
      isActive: {
        name: 'Active',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <>
                  <CheckCircle className="text-green-600 w-4 h-4 mr-1" />
                  <span className="text-green-600 hidden">Active</span>
                </>
              ) : (
                <>
                  <XCircle className="text-red-600 w-4 h-4 mr-1" />
                  <span className="text-red-600 hidden">Inactive</span>
                </>
              )}
            </div>
          ),
        },
        sortable: true,
      },
      // contactPersonName: {
      //   name: 'Contact Person',
      //   type: 'text',
      //   sortable: false,
      // },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <Link
              href={`/dashboard/setup/master-setup/advance-receiving-warehouse/${row.id ?? 'add'}`}
            >
              <Button
                size="sm"
                variant="secondary"
                iconName="EditIcon"
              ></Button>
            </Link>
          ),
        },
      },
    },
    false,
  );

  return (
    <DataTable
      data={data}
      columns={columns}
      filterFields={filters}
      isLoading={isLoading || isLoadingProvinces}
      controls={
        <Link href="/dashboard/setup/master-setup/advance-receiving-warehouse/add">
          <Button
            variant={'main'}
            iconName="AddIcon"
            iconProps={{ className: 'text-white' }}
          >
            Add Warehouse
          </Button>
        </Link>
      }
    />
  );
};

export default AdvanceReceivingWarehouseTable;
