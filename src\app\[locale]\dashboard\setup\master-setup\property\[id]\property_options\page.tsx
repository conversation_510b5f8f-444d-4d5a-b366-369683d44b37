import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { getQueryClient } from '@/utils/query-client';
import PropertyOptionQuery from '@/services/queries/PropertyOptionQuery'; // Import the PropertyOptionQuery service
import PropertyOptionsTable from '../components/property_options_table';

export default async function PropertyOptionPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;

    const client = getQueryClient();

    if (id !== 'add') {
      if (Number.isNaN(Number(id))) throw new Error('Invalid property id');

      // Fetch PropertyOptions for the specific property
      await client.prefetchQuery({
        queryKey: ['PropertyOptions', { propertyId: Number(id) }],
        queryFn: () => PropertyOptionQuery.getByPropertyId(Number(id)),
      });
    }

    return (
      <div className="space-y-4 px-2">
        <h2 className="text-xl font-semibold text-[#00646C] border-b border-slate-200 pb-3">
          List of Property Options
        </h2>
        <HydrationBoundary state={dehydrate(client)}>
          <PropertyOptionsTable propertyId={Number(id)} />
        </HydrationBoundary>
      </div>
    );
  } catch (error) {
    // In case of error, redirect to the "add" page for PropertyOptions
    redirect('/dashboard/setup/master-setup/property/add');
  }
}
