import React from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  ForkliftServiceSchema,
  ForkliftServiceFormType,
} from '@/schema/ServiceFormSchemas';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';

interface ForkliftServiceFormProps {
  onSubmit: (data: ForkliftServiceFormType) => void;
  initialData?: Partial<ForkliftServiceFormType>;
  isLoading?: boolean;
}

export function ForkliftServiceForm({
  onSubmit,
  initialData,
  isLoading = false,
}: ForkliftServiceFormProps) {
  const form = useForm<ForkliftServiceFormType>({
    resolver: zodResolver(ForkliftServiceSchema),
    defaultValues: {
      rates: initialData?.rates || [
        { weight: 5000, regularRate: 50, overtimeRate: 80, doubleRate: 100 },
        { weight: 10000, regularRate: 50, overtimeRate: 80, doubleRate: 100 },
        { weight: 20000, regularRate: 50, overtimeRate: 80, doubleRate: 100 },
      ],
      daysHours: {
        regular: initialData?.daysHours?.regular || '',
        regularMinimumCharge:
          initialData?.daysHours?.regularMinimumCharge || 100,
        overtime: initialData?.daysHours?.overtime || '',
        overtimeMinimumCharge:
          initialData?.daysHours?.overtimeMinimumCharge || 200,
        doubleTime: initialData?.daysHours?.doubleTime || '',
        doubleTimeMinimumCharge:
          initialData?.daysHours?.doubleTimeMinimumCharge || 400,
      },
      additionalServices: {
        manCageRate: initialData?.additionalServices?.manCageRate || 10,
        boomRate: initialData?.additionalServices?.boomRate || 100,
      },
    },
  });

  const { fields } = useFieldArray({
    control: form.control,
    name: 'rates',
  });

  const handleSubmit = (data: ForkliftServiceFormType) => {
    onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Rates Table */}
        <div className="space-y-4">
          <FormLabel className="text-base font-medium">Rates:</FormLabel>
          <div className="grid grid-cols-4 gap-4 text-sm font-medium">
            <div>Weight up to (lbs.)</div>
            <div>Regular Rate ($/hr)</div>
            <div>Overtime Rate ($/hr)</div>
            <div>Double Rate ($/hr)</div>
          </div>

          {fields.map((field, index) => (
            <div key={field.id} className="grid grid-cols-4 gap-4">
              <FormField
                control={form.control}
                name={`rates.${index}.weight`}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseInt(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`rates.${index}.regularRate`}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`rates.${index}.overtimeRate`}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`rates.${index}.doubleRate`}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          ))}
        </div>

        {/* Days & Hours Section */}
        <div className="space-y-4">
          <FormLabel className="text-base font-medium">Days & Hours:</FormLabel>

          <div className="space-y-4">
            <div>
              <FormLabel className="text-sm">Regular:</FormLabel>
              <FormField
                control={form.control}
                name="daysHours.regular"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea
                        placeholder="Specify regular hours..."
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="daysHours.regularMinimumCharge"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Charge:</FormLabel>
                    <FormControl>
                      <div className="relative max-w-xs">
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseFloat(e.target.value) || 0)
                          }
                        />
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                          $
                        </span>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div>
              <FormLabel className="text-sm">Overtime:</FormLabel>
              <FormField
                control={form.control}
                name="daysHours.overtime"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea
                        placeholder="Specify overtime hours..."
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="daysHours.overtimeMinimumCharge"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Charge:</FormLabel>
                    <FormControl>
                      <div className="relative max-w-xs">
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseFloat(e.target.value) || 0)
                          }
                        />
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                          $
                        </span>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div>
              <FormLabel className="text-sm">Double Time:</FormLabel>
              <FormField
                control={form.control}
                name="daysHours.doubleTime"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea
                        placeholder="Specify double time hours..."
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="daysHours.doubleTimeMinimumCharge"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Charge:</FormLabel>
                    <FormControl>
                      <div className="relative max-w-xs">
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseFloat(e.target.value) || 0)
                          }
                        />
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                          $
                        </span>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </div>

        {/* Additional Services */}
        <div className="space-y-4">
          <FormLabel className="text-base font-medium">
            Additional Services:
          </FormLabel>
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="additionalServices.manCageRate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Man Cage Rate:</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        $ / hr
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="additionalServices.boomRate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Boom Rate:</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        $ / hr
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Saving...' : 'Save Information'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
