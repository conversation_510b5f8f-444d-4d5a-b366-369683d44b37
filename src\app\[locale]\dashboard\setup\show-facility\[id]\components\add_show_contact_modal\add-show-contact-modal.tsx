'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Form } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import Field from '@/components/ui/inputs/field';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { modal } from '@/components/ui/overlay';
import { useToast } from '@/components/ui/use-toast';
import { Spinner } from '@/components/ui/spinner';
import Suspense from '@/components/ui/Suspense';
import { ShowContactSchema, ShowContactData } from '@/schema/ShowContactSchema';
import ShowContactQuery from '@/services/queries/ShowContactQuery';
import { getQueryClient } from '@/utils/query-client';
import ContactTypeQuery from '@/services/queries/ContactTypeQuery';
import { useRouter } from 'next/navigation';

function FormContent({
  defaultValues,
  locationId,
  id,
}: {
  defaultValues?: ShowContactData;
  locationId: number;
  id?: number;
}) {
  const { toast } = useToast();

  const form = useForm<ShowContactData>({
    resolver: zodResolver(ShowContactSchema),
    defaultValues: defaultValues ?? {
      locationId,
      contactTypeId: undefined,
      companyId: undefined,
      firstName: undefined,
      lastName: undefined,
      email: undefined,
      telephone: undefined,
      ext: undefined,
      cellphone: undefined,
      isArchived: false,
    },
  });

  const { data: contactTypes } = useQuery({
    queryKey: [...ContactTypeQuery.tags],
    queryFn: () => ContactTypeQuery.getAll(),
  });
  const { push } = useRouter();
  const { mutate, isPending } = useMutation({
    mutationFn: id ? ShowContactQuery.update(id) : ShowContactQuery.create,
    onSuccess: async () => {
      const client = getQueryClient();
      await client.invalidateQueries({
        queryKey: ['ShowContact', { locationId }],
      });

      toast({
        title: 'Success',
        description: id
          ? 'Contact updated successfully'
          : 'Contact added successfully',
        variant: 'success',
      });     
      modal.close();
    },
    onError: (error: any) => {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Something went wrong',
      });
    },
  });

  return (
    <Form {...form}>
      <ModalContainer
        className="w-full max-w-2xl"
        title={id ? 'Update Contact' : 'Add Contact'}
        description={id ? 'Update contact details' : 'Create a new contact'}
        onSubmit={form.handleSubmit((data) => mutate(data))}
        controls={
          <div className="flex justify-end items-center gap-4">
            <Button
              variant={'main'}
              disabled={isPending}
              iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
              iconProps={{ className: isPending ? 'animate-spin' : '' }}
            >
              {isPending ? 'Saving...' : 'Save'}
            </Button>
          </div>
        }
      >
        <div className="grid grid-cols-1 gap-4">
          <Field
            control={form.control}
            name="contactTypeId"
            label="Contact Type"
            type={{
              type: 'select',
              props: {
                placeholder: 'Select contact type',
                options:
                  contactTypes?.map((type) => ({
                    label: type.name,
                    value: String(type.id),
                  })) || [],
              },
            }}
            required
          />
          <Field
            name="firstName"
            label="First Name"
            type="text"
            control={form.control}
            placeholder="Enter First Name"
            required
          />
          <Field
            name="lastName"
            label="Last Name"
            type="text"
            control={form.control}
            placeholder="Enter Last Name"
            required
          />
          <Field
            name="email"
            label="Email"
            type="email"
            placeholder="Enter Email"
            control={form.control}
          />
          <Field
            name="telephone"
            label="Telephone"
            type="text"
            placeholder="Enter Telephone"
            control={form.control}
          />
          <Field
            name="ext"
            label="Extention"
            type="text"
            control={form.control}
            placeholder="Enter Extention"
          />
          <Field
            name="cellphone"
            label="Cellphone"
            placeholder="Enter Cellphone"
            type="text"
            control={form.control}
          />
          {id && (
            <Field
              name="isArchived"
              label="Archived"
              type="checkbox"
              control={form.control}
            />
          )}
        </div>
      </ModalContainer>
    </Form>
  );
}

interface AddContactModalProps {
  locationId: number;
  contactId?: number;
}

function AddShowContactModal({ locationId, contactId }: AddContactModalProps) {
  const {
    data: contact,
    isLoading,
    isPaused,
  } = useQuery({
    queryKey: ['ShowContact', { contactId }],
    queryFn: () => ShowContactQuery.getById(contactId!),
    enabled: !!contactId,
    select: (data) =>
      ({
        locationId,
        contactTypeId: String(data.contactTypeId) ?? undefined,
        firstName: data.firstName ?? undefined,
        lastName: data.lastName ?? undefined,
        email: data.email ?? undefined,
        telephone: data.telephone ?? undefined,
        ext: data.ext ?? undefined,
        cellphone: data.cellphone ?? undefined,
        isArchived: data.isArchived ?? false,
      }) as any,
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      {isLoading && isPaused ? (
        <Spinner />
      ) : (
        <FormContent
          locationId={locationId}
          id={contactId}
          defaultValues={contact}
        />
      )}
    </Suspense>
  );
}

export default AddShowContactModal;
