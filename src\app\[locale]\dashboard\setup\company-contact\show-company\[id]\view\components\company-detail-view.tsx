'use client';

import { useQuery } from '@tanstack/react-query';
import Link from 'next/link';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { Button } from '@/components/ui/button';
import CardGenerator from '@/components/ui/card_generator/card-generator';
import { ContactsTable } from '../../../components/contacts_table/contacts-table';
import { ShowsTable } from '../../../components/shows_table';
import {
  InfoIcon,
  DocumentIcon,
  SettingsIcon,
  UserIcon,
  EyeIcon,
  MapPinIcon,
  CheckCircleIcon,
  CalendarIcon,
} from '@/assets/Icons';

interface CompanyDetailViewProps {
  companyId: number;
}

export default function CompanyDetailView({
  companyId,
}: CompanyDetailViewProps) {
  const {
    data: company,
    isLoading,
    error,
  } = useQuery({
    queryKey: [...CompanyQuery.tags, { id: companyId }],
    queryFn: () => CompanyQuery.getOne(companyId),
  });

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (error || !company) {
    return (
      <CardGenerator
        title="Company Not Found"
        description="The company you're looking for doesn't exist or has been deleted."
      >
        <div className="text-center py-8">
          <Link href="/dashboard/setup/company-contact/show-company">
            <Button variant="outline" iconName="BackIcon">
              Back to Companies
            </Button>
          </Link>
        </div>
      </CardGenerator>
    );
  }

  return (
    <div className="space-y-4">
      {/* Company Information Card */}
      <CardGenerator
        title={
          <div className="flex justify-between items-center w-full">
            <div>
              <h3 className="text-lg font-semibold">{`${company.name} ${company.isArchived ? '(Archived)' : ''}`}</h3>
              <p className="text-sm text-gray-500">{`Company ID: ${company.id}`}</p>
            </div>
            <Link
              href={`/dashboard/setup/company-contact/show-company/${companyId}`}
            >
              <Button
                variant="primary"
                iconName="EditIcon"
                iconProps={{ className: 'text-white' }}
              >
                Edit Company
              </Button>
            </Link>
          </div>
        }
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Company Name */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <InfoIcon size={16} className="text-gray-600" />
              <label className="text-sm font-semibold text-gray-700">
                Company Name
              </label>
            </div>
            <p className="text-gray-900 font-bold text-lg pl-6">
              {company.name}
            </p>
          </div>

          {/* Account Number */}
          {company.accountNumber && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <DocumentIcon size={16} className="text-gray-600" />
                <label className="text-sm font-semibold text-gray-700">
                  Account Number
                </label>
              </div>
              <p className="text-gray-900 font-medium pl-6">
                {company.accountNumber}
              </p>
            </div>
          )}

          {/* Company Group */}
          {company.companyGroup && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <SettingsIcon size={16} className="text-gray-600" />
                <label className="text-sm font-semibold text-gray-700">
                  Company Group
                </label>
              </div>
              <div className="pl-6">
                <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  {company.companyGroup}
                </span>
              </div>
            </div>
          )}

          {/* Phone */}
          {company.phone && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <InfoIcon size={16} className="text-gray-600" />
                <label className="text-sm font-semibold text-gray-700">
                  Phone
                </label>
              </div>
              <div className="pl-6">
                <a
                  href={`tel:${company.phone}`}
                  className="text-gray-900 font-medium hover:text-gray-700 transition-colors underline decoration-transparent hover:decoration-current"
                >
                  {company.phone}
                </a>
              </div>
            </div>
          )}

          {/* Email */}
          {company.email && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <UserIcon size={16} className="text-gray-600" />
                <label className="text-sm font-semibold text-gray-700">
                  Email
                </label>
              </div>
              <div className="pl-6">
                <a
                  href={`mailto:${company.email}`}
                  className="text-gray-900 font-medium hover:text-gray-700 transition-colors break-all underline decoration-transparent hover:decoration-current"
                >
                  {company.email}
                </a>
              </div>
            </div>
          )}

          {/* Website */}
          {company.websiteUrl && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <EyeIcon size={16} className="text-gray-600" />
                <label className="text-sm font-semibold text-gray-700">
                  Website
                </label>
              </div>
              <div className="pl-6">
                <a
                  href={
                    company.websiteUrl.startsWith('http')
                      ? company.websiteUrl
                      : `https://${company.websiteUrl}`
                  }
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-900 font-medium hover:text-gray-700 transition-colors break-all inline-flex items-center gap-1 underline decoration-transparent hover:decoration-current"
                >
                  {company.websiteUrl}
                  <EyeIcon size={12} className="text-gray-400" />
                </a>
              </div>
            </div>
          )}

          {/* Address */}
          {company.address1 && (
            <div className="md:col-span-2 lg:col-span-3 space-y-2">
              <div className="flex items-center gap-2">
                <MapPinIcon size={16} className="text-gray-600" />
                <label className="text-sm font-semibold text-gray-700">
                  Address
                </label>
              </div>
              <div className="text-gray-900 font-medium pl-6 space-y-1">
                <p>{company.address1}</p>
                {company.address2 && <p>{company.address2}</p>}
                {company.city && (
                  <p>
                    {company.city}
                    {company.postalCode && `, ${company.postalCode}`}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Notes */}
          {company.note && (
            <div className="md:col-span-2 lg:col-span-3 space-y-2">
              <div className="flex items-center gap-2">
                <DocumentIcon size={16} className="text-gray-600" />
                <label className="text-sm font-semibold text-gray-700">
                  Notes
                </label>
              </div>
              <p className="text-gray-900 font-medium leading-relaxed pl-6">
                {company.note}
              </p>
            </div>
          )}

          {/* Status */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <CheckCircleIcon
                size={16}
                className={
                  company.isArchived ? 'text-gray-500' : 'text-gray-600'
                }
              />
              <label className="text-sm font-semibold text-gray-700">
                Status
              </label>
            </div>
            <div className="flex items-center gap-2 pl-6">
              <div
                className={`w-2 h-2 rounded-full ${
                  company.isArchived ? 'bg-gray-400' : 'bg-success'
                }`}
              />
              <span
                className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                  company.isArchived
                    ? 'bg-gray-100 text-gray-800'
                    : 'bg-gray-100 text-success'
                }`}
              >
                {company.isArchived ? 'Archived' : 'Active'}
              </span>
            </div>
          </div>
        </div>
      </CardGenerator>

      {/* Contacts Section */}
      <CardGenerator
        title={
          <div className="flex justify-between items-center w-full">
            <h3 className="text-lg font-semibold">Company Contacts</h3>
            <Link
              href={`/dashboard/setup/company-contact/show-company/${companyId}/contacts/add`}
            >
              <Button variant="main" iconName="AddIcon">
                Add Contact
              </Button>
            </Link>
          </div>
        }
      >
        <ContactsTable companyId={companyId} companyName={company.name} />
      </CardGenerator>

      {/* Shows Section - Only for Show Manager companies */}
      {company.companyGroup === 'Show manager' && (
        <CardGenerator
          title={
            <div className="flex justify-between items-center w-full">
              <div className="flex items-center gap-2">
                <CalendarIcon size={20} className="text-gray-600" />
                <h3 className="text-lg font-semibold">Managed Shows</h3>
              </div>
              <Link href="/dashboard/shows/add">
                <Button variant="main" iconName="AddIcon">
                  Add Show
                </Button>
              </Link>
            </div>
          }
        >
          <ShowsTable companyId={companyId} companyName={company.name} />
        </CardGenerator>
      )}
    </div>
  );
}
