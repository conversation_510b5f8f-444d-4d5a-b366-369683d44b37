'use client';

import { useQuery } from '@tanstack/react-query';
import Link from 'next/link';
import { Building2, MapPin, Phone, Mail, Globe, User } from 'lucide-react';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/Badge';
import { ContactsTable } from '../../../components/contacts_table/contacts-table';

interface CompanyDetailViewProps {
  companyId: number;
}

export default function CompanyDetailView({
  companyId,
}: CompanyDetailViewProps) {
  const {
    data: company,
    isLoading,
    error,
  } = useQuery({
    queryKey: [...CompanyQuery.tags, { id: companyId }],
    queryFn: () => CompanyQuery.getOne(companyId),
  });

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
          </div>
          <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>
    );
  }

  if (error || !company) {
    return (
      <div className="text-center py-12">
        <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Company not found
        </h3>
        <p className="text-gray-500 mb-4">
          The company you're looking for doesn't exist or has been deleted.
        </p>
        <Link href="/dashboard/setup/company-contact/show-company">
          <Button variant="outline">Back to Companies</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-blue-100 rounded-lg">
            <Building2 className="h-8 w-8 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              {company.name}
              {company.isArchived && (
                <Badge variant="secondary" className="text-xs">
                  Archived
                </Badge>
              )}
            </h1>
            <p className="text-gray-500">Company ID: {company.id}</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Link
            href={`/dashboard/setup/company-contact/show-company/${companyId}`}
          >
            <Button variant="outline" iconName="EditIcon">
              Edit Company
            </Button>
          </Link>
          <Link href="/dashboard/setup/company-contact/show-company">
            <Button variant="secondary">Back to Companies</Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Company Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Company Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Company Name
                  </label>
                  <p className="text-gray-900 font-medium">{company.name}</p>
                </div>
                {company.accountNumber && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Account Number
                    </label>
                    <p className="text-gray-900">{company.accountNumber}</p>
                  </div>
                )}
                {company.companyGroup && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Company Group
                    </label>
                    <p className="text-gray-900">{company.companyGroup}</p>
                  </div>
                )}
              </div>

              {company.note && (
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Notes
                  </label>
                  <p className="text-gray-900">{company.note}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Phone className="h-5 w-5" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {company.phone && (
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <div>
                      <label className="text-sm font-medium text-gray-500">
                        Phone
                      </label>
                      <p className="text-gray-900">{company.phone}</p>
                    </div>
                  </div>
                )}
                {company.email && (
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <div>
                      <label className="text-sm font-medium text-gray-500">
                        Email
                      </label>
                      <p className="text-gray-900">{company.email}</p>
                    </div>
                  </div>
                )}
                {company.websiteUrl && (
                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-gray-400" />
                    <div>
                      <label className="text-sm font-medium text-gray-500">
                        Website
                      </label>
                      <a
                        href={company.websiteUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 underline"
                      >
                        {company.websiteUrl}
                      </a>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Address Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Address Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {company.address1 && (
                  <div className="md:col-span-2">
                    <label className="text-sm font-medium text-gray-500">
                      Address
                    </label>
                    <p className="text-gray-900">
                      {company.address1}
                      {company.address2 && (
                        <>
                          <br />
                          {company.address2}
                        </>
                      )}
                    </p>
                  </div>
                )}
                {company.city && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      City
                    </label>
                    <p className="text-gray-900">{company.city}</p>
                  </div>
                )}
                {company.postalCode && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Postal Code
                    </label>
                    <p className="text-gray-900">{company.postalCode}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <div
                  className={`w-3 h-3 rounded-full ${company.isArchived ? 'bg-gray-400' : 'bg-green-500'}`}
                ></div>
                <span
                  className={`font-medium ${company.isArchived ? 'text-gray-600' : 'text-green-600'}`}
                >
                  {company.isArchived ? 'Archived' : 'Active'}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Link
                href={`/dashboard/setup/company-contact/show-company/${companyId}/contacts/add`}
                className="block"
              >
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  iconName="AddIcon"
                >
                  Add Contact
                </Button>
              </Link>
              <Link
                href={`/dashboard/setup/company-contact/show-company/${companyId}`}
                className="block"
              >
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  iconName="EditIcon"
                >
                  Edit Company
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Contacts Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Company Contacts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ContactsTable companyId={companyId} companyName={company.name} />
        </CardContent>
      </Card>
    </div>
  );
}
