import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { getQueryClient } from '@/utils/query-client';
import AppLayout from '@/components/ui/app_layout';
import WarehouseQuery from '@/services/queries/WarehouseQuery';
import WarehouseForm from './component/warehouse_form';

export default async function WarehousePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;
    const isAdd = id === 'add';

    const client = getQueryClient();

    if (!isAdd) {
      if (Number.isNaN(Number(id))) throw new Error();
      await client.prefetchQuery({
        queryKey: ['Warehouse Info', { id }],
        queryFn: () => WarehouseQuery.get(Number(id!)),
      });
    }

    const breadcrumbItems = [
      { title: 'Setup', link: '/dashboard/setup' },
      { title: 'Master-Setup', link: '/dashboard/setup/master-setup' },
      {
        title: 'Advance Receiving Warehouse',
        link: '/dashboard/setup/master-setup/advance-receiving-warehouse',
      },
      {
        title: isAdd ? 'Add Warehouse' : 'Edit Warehouse',
        link: `/dashboard/setup/master-setup/advance-receiving-warehouse/${id}`,
      },
    ];

    return (
      <AppLayout items={breadcrumbItems}>
        <HydrationBoundary state={dehydrate(client)}>
          <WarehouseForm id={isAdd ? undefined : Number(id)} />
        </HydrationBoundary>
      </AppLayout>
    );
  } catch (error) {
    redirect('/dashboard/setup/master-setup/advance-receiving-warehouse/add');
  }
}
