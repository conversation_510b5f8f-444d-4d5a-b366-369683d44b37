import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import UnderDevelopment from './UnderDevelopment';
import { ShowSchedule } from '@/models/Show';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import { useMemo } from 'react';
import { ErrorNotificationIcon, SuccessNotificationIcon } from '@/assets/Icons';

function formatDate(dateString: string) {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric',
  });
}

export default function Schedule({
  show,
  schedules,
}: {
  show: any;
  schedules: ShowSchedule[] | undefined;
}) {
  const columns = useMemo(
    () =>
      generateTableColumns<ShowSchedule>({
        showScheduleDate: {
          name: 'Date',
          type: {
            type: 'node',
            render: ({ row }) => formatDate(row.showScheduleDate),
          },
          sortable: true,
        },
        timeStart: { name: 'Start Time', type: 'text' },
        timeEnd: { name: 'End Time', type: 'text' },
        showScheduleConfirmed: {
          name: 'Confirmed',
          type: {
            type: 'node',
            render: ({ row }) => (
              <div className="flex items-center whitespace-nowrap">
                {row.showScheduleConfirmed ? (
                  <>
                    <SuccessNotificationIcon className="text-green-600 w-4 h-4 mr-1" />
                    <span className="text-green-600 hidden">Yes</span>
                  </>
                ) : (
                  <>
                    <ErrorNotificationIcon className="text-red-600 w-4 h-4 mr-1" />
                    <span className="text-red-600 hidden">No</span>
                  </>
                )}
              </div>
            ),
          },
        },
      }),
    [],
  );

  const filters = useMemo(
    () =>
      generateTableFilters<ShowSchedule>({
        showScheduleDate: { name: 'Date', type: 'text' },
        showScheduleConfirmed: {
          name: 'Confirmed',
          type: {
            type: 'select',
            options: [
              { label: 'Yes', value: 'true' },
              { label: 'No', value: 'false' },
            ],
          },
        },
      }),
    [],
  );

  if (!schedules || schedules.length === 0) {
    return <UnderDevelopment sectionName="Show Schedule" />;
  }

  return (
    <div>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Show Schedule</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={schedules}
            isLoading={false} // schedules are already loaded by parent
            filterFields={filters}
          />
        </CardContent>
      </Card>
    </div>
  );
}
