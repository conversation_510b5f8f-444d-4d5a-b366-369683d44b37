import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  OnSiteMaterialHandlingSchema,
  OnSiteMaterialHandlingFormType,
} from '@/schema/ServiceFormSchemas';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface OnSiteMaterialHandlingFormProps {
  onSubmit: (data: OnSiteMaterialHandlingFormType) => void;
  initialData?: Partial<OnSiteMaterialHandlingFormType>;
  isLoading?: boolean;
}

export function OnSiteMaterialHandlingForm({
  onSubmit,
  initialData,
  isLoading = false,
}: OnSiteMaterialHandlingFormProps) {
  const form = useForm<OnSiteMaterialHandlingFormType>({
    resolver: zodResolver(OnSiteMaterialHandlingSchema),
    defaultValues: {
      receivingAddress:
        initialData?.receivingAddress ||
        'Vancouver Convention Centre East\n999 Canada Place Vancouver\nBritish Columbia Canada',
      daysHours: initialData?.daysHours || '',
      pricePerWeight: initialData?.pricePerWeight || 0,
      weight: initialData?.weight || 0,
      weightUnit: initialData?.weightUnit || 'lbs',
      minimumCharge: initialData?.minimumCharge || 0,
    },
  });

  const handleSubmit = (data: OnSiteMaterialHandlingFormType) => {
    onSubmit(data);
  };

  return (
    <div className="space-y-6">
      <div className="border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          On-Site Material Handling
        </h3>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4"
          >
            <div>
              <FormLabel className="text-sm font-medium">
                Receiving Address:
              </FormLabel>
              <FormField
                control={form.control}
                name="receivingAddress"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea
                        className="min-h-[80px] bg-gray-50"
                        readOnly
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div>
              <FormLabel className="text-sm font-medium">
                Please arrange shipments to arrive between:
              </FormLabel>
              <FormField
                control={form.control}
                name="daysHours"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea
                        placeholder="monday"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="pricePerWeight"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Price/Weight:</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="1.00"
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseFloat(e.target.value) || 0)
                          }
                        />
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                          $
                        </span>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="weight"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Weight:</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="1"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="weightUnit"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Unit:</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="lbs">lbs</SelectItem>
                        <SelectItem value="kg">kg</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="minimumCharge"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Minimum charge:</FormLabel>
                  <FormControl>
                    <div className="relative max-w-xs">
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="94.00"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        $
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
              <p className="text-sm text-yellow-800">
                <strong>Note:</strong> There are currently orders on this
                service. Rate cannot be modified.
              </p>
            </div>

            <div className="flex justify-end">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save Information'}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
