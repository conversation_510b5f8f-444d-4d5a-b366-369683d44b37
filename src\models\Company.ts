export interface CompanyInList {
  id: number;
  name: string;
  phone?: string;
  email?: string;
  address1?: string;
  address2?: string;
  city?: string;
  province?: string;
  postalCode?: string;
  country?: string;
  websiteUrl?: string;
  accountNumber?: string;
  companyGroup?: string;
  note?: string;
  isArchived: boolean;
  numberOfShows?: number;
}

export interface Company {
  id: number;
  name: string;
  phone?: string;
  email?: string;
  address1?: string;
  address2?: string;
  city?: string;
  provinceId?: number;
  postalCode?: string;
  countryId?: number;
  websiteUrl?: string;
  accountNumber?: string;
  companyGroup?: string;
  note?: string;
  isArchived: boolean;
}

export interface CompanyCreateRequest {
  name: string;
  phone?: string;
  email?: string;
  address1?: string;
  address2?: string;
  city?: string;
  provinceId?: number;
  postalCode?: string;
  countryId?: number;
  websiteUrl?: string;
  accountNumber?: string;
  companyGroup?: string;
  note?: string;
  isArchived?: boolean;
}

export interface CompanyUpdateRequest {
  name: string;
  phone?: string;
  email?: string;
  address1?: string;
  address2?: string;
  city?: string;
  provinceId?: number;
  postalCode?: string;
  countryId?: number;
  websiteUrl?: string;
  accountNumber?: string;
  companyGroup?: string;
  note?: string;
  isArchived?: boolean;
}

export interface CompanyShow {
  id: number;
  name: string;
  code: string;
  startDate: string;
  endDate: string;
  venueName: string;
  city: string;
  province: string;
  archive: boolean;
}
