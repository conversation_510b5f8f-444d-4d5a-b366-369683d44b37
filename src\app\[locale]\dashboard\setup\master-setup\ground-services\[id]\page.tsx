import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import DocumentFileTypeQuery from '@/services/queries/DocumentFileTypeQuery';
import { getQueryClient } from '@/utils/query-client';
import AppLayout from '@/components/ui/app_layout';
import GroundServiceForm from './components/ground_service_form';

export default async function DocumentFileTypePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;
    const isAdd = id === 'add';

    const client = getQueryClient();

    if (!isAdd) {
      if (Number.isNaN(Number(id))) throw new Error();
      await client.prefetchQuery({
        queryKey: [...DocumentFileTypeQuery.tags, { id: Number(id) }],
        queryFn: () => DocumentFileTypeQuery.getOne(Number(id)),
      });
    }

    const breadcrumbItems = [
      { title: 'Setup', link: '/dashboard/setup' },
      { title: 'Master Setup', link: '/dashboard/setup/master-setup' },
      {
        title: 'Ground Services',
        link: '/dashboard/setup/master-setup/ground-services',
      },
      {
        title: isAdd ? 'Add Ground Service' : 'Edit Ground Service',
        link: `/dashboard/setup/master-setup/ground-services/${id}`,
      },
    ];

    return (
      <AppLayout items={breadcrumbItems}>
        <HydrationBoundary state={dehydrate(client)}>
          <GroundServiceForm serviceId={isAdd ? undefined : Number(id)} />
        </HydrationBoundary>
      </AppLayout>
    );
  } catch (error) {
    redirect('/dashboard/setup/master-setup/ground-services/add');
  }
}
