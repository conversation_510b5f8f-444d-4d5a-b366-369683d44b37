'use client';

import { useQuery } from '@tanstack/react-query';
import Link from 'next/link';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { Button } from '@/components/ui/button';
import CardGenerator from '@/components/ui/card_generator/card-generator';
import { ContactsTable } from '../../../components/contacts_table/contacts-table';
import {
  MapPinIcon,
  CheckCircleIcon,
  InfoIcon,
  DocumentIcon,
  SettingsIcon,
  UserIcon,
  EyeIcon,
} from '@/assets/Icons';

interface CompanyDetailViewProps {
  companyId: number;
}

export default function CompanyDetailView({
  companyId,
}: CompanyDetailViewProps) {
  const {
    data: company,
    isLoading,
    error,
  } = useQuery({
    queryKey: [...CompanyQuery.tags, { id: companyId }],
    queryFn: () => CompanyQuery.getOne(companyId),
  });

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (error || !company) {
    return (
      <CardGenerator
        title="Company Not Found"
        description="The company you're looking for doesn't exist or has been deleted."
      >
        <div className="text-center py-8">
          <Link href="/dashboard/setup/company-contact/exhibitor-company">
            <Button variant="outline" iconName="BackIcon">
              Back to Companies
            </Button>
          </Link>
        </div>
      </CardGenerator>
    );
  }

  return (
    <div className="space-y-4">
      {/* Company Information Card */}
      <CardGenerator
        title={
          <div className="flex justify-between items-center w-full">
            <div>
              <h3 className="text-lg font-semibold">{`${company.name} ${company.isArchived ? '(Archived)' : ''}`}</h3>
              <p className="text-sm text-gray-500">{`Company ID: ${company.id} • Exhibitor Company`}</p>
            </div>
            <Link
              href={`/dashboard/setup/company-contact/exhibitor-company/${companyId}`}
            >
              <Button variant="outline" iconName="EditIcon">
                Edit Company
              </Button>
            </Link>
          </div>
        }
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Company Name */}
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-100">
            <div className="flex items-center gap-2 mb-2">
              <InfoIcon size={18} className="text-blue-600" />
              <label className="text-sm font-semibold text-blue-700">
                Company Name
              </label>
            </div>
            <p className="text-gray-900 font-bold text-lg">{company.name}</p>
          </div>

          {/* Account Number */}
          {company.accountNumber && (
            <div className="bg-gradient-to-br from-purple-50 to-violet-50 p-4 rounded-lg border border-purple-100">
              <div className="flex items-center gap-2 mb-2">
                <DocumentIcon size={18} className="text-purple-600" />
                <label className="text-sm font-semibold text-purple-700">
                  Account Number
                </label>
              </div>
              <p className="text-gray-900 font-medium">
                {company.accountNumber}
              </p>
            </div>
          )}

          {/* Company Group */}
          <div className="bg-gradient-to-br from-emerald-50 to-green-50 p-4 rounded-lg border border-emerald-100">
            <div className="flex items-center gap-2 mb-2">
              <SettingsIcon size={18} className="text-emerald-600" />
              <label className="text-sm font-semibold text-emerald-700">
                Company Group
              </label>
            </div>
            <div className="flex items-center gap-2">
              <span className="bg-emerald-100 text-emerald-800 text-sm font-medium px-2.5 py-1 rounded-full">
                Exhibitor
              </span>
            </div>
          </div>

          {/* Phone */}
          {company.phone && (
            <div className="bg-gradient-to-br from-orange-50 to-amber-50 p-4 rounded-lg border border-orange-100">
              <div className="flex items-center gap-2 mb-2">
                <InfoIcon size={18} className="text-orange-600" />
                <label className="text-sm font-semibold text-orange-700">
                  Phone
                </label>
              </div>
              <a
                href={`tel:${company.phone}`}
                className="text-gray-900 font-medium hover:text-orange-600 transition-colors"
              >
                {company.phone}
              </a>
            </div>
          )}

          {/* Email */}
          {company.email && (
            <div className="bg-gradient-to-br from-cyan-50 to-blue-50 p-4 rounded-lg border border-cyan-100">
              <div className="flex items-center gap-2 mb-2">
                <UserIcon size={18} className="text-cyan-600" />
                <label className="text-sm font-semibold text-cyan-700">
                  Email
                </label>
              </div>
              <a
                href={`mailto:${company.email}`}
                className="text-gray-900 font-medium hover:text-cyan-600 transition-colors break-all"
              >
                {company.email}
              </a>
            </div>
          )}

          {/* Website */}
          {company.websiteUrl && (
            <div className="bg-gradient-to-br from-indigo-50 to-purple-50 p-4 rounded-lg border border-indigo-100">
              <div className="flex items-center gap-2 mb-2">
                <EyeIcon size={18} className="text-indigo-600" />
                <label className="text-sm font-semibold text-indigo-700">
                  Website
                </label>
              </div>
              <a
                href={
                  company.websiteUrl.startsWith('http')
                    ? company.websiteUrl
                    : `https://${company.websiteUrl}`
                }
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-900 font-medium hover:text-indigo-600 transition-colors break-all inline-flex items-center gap-1"
              >
                {company.websiteUrl}
                <EyeIcon size={14} className="text-indigo-500" />
              </a>
            </div>
          )}

          {/* Address */}
          {company.address1 && (
            <div className="md:col-span-2 lg:col-span-3 bg-gradient-to-br from-slate-50 to-gray-50 p-4 rounded-lg border border-slate-200">
              <div className="flex items-center gap-2 mb-3">
                <MapPinIcon size={18} className="text-slate-600" />
                <label className="text-sm font-semibold text-slate-700">
                  Address
                </label>
              </div>
              <div className="text-gray-900 font-medium space-y-1">
                <p>{company.address1}</p>
                {company.address2 && <p>{company.address2}</p>}
                {company.city && (
                  <p className="flex items-center gap-1">
                    <span>{company.city}</span>
                    {company.postalCode && <span>, {company.postalCode}</span>}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Notes */}
          {company.note && (
            <div className="md:col-span-2 lg:col-span-3 bg-gradient-to-br from-yellow-50 to-amber-50 p-4 rounded-lg border border-yellow-200">
              <div className="flex items-center gap-2 mb-3">
                <DocumentIcon size={18} className="text-yellow-600" />
                <label className="text-sm font-semibold text-yellow-700">
                  Notes
                </label>
              </div>
              <p className="text-gray-900 font-medium leading-relaxed">
                {company.note}
              </p>
            </div>
          )}

          {/* Status */}
          <div
            className={`p-4 rounded-lg border ${
              company.isArchived
                ? 'bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200'
                : 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200'
            }`}
          >
            <div className="flex items-center gap-2 mb-2">
              <CheckCircleIcon
                size={18}
                className={
                  company.isArchived ? 'text-gray-500' : 'text-green-600'
                }
              />
              <label
                className={`text-sm font-semibold ${
                  company.isArchived ? 'text-gray-600' : 'text-green-700'
                }`}
              >
                Status
              </label>
            </div>
            <div className="flex items-center gap-2">
              <div
                className={`w-3 h-3 rounded-full ${
                  company.isArchived ? 'bg-gray-400' : 'bg-green-500'
                }`}
              />
              <span
                className={`font-bold text-sm px-2.5 py-1 rounded-full ${
                  company.isArchived
                    ? 'bg-gray-100 text-gray-700'
                    : 'bg-green-100 text-green-800'
                }`}
              >
                {company.isArchived ? 'Archived' : 'Active'}
              </span>
            </div>
          </div>
        </div>
      </CardGenerator>

      {/* Contacts Section */}
      <CardGenerator
        title={
          <div className="flex justify-between items-center w-full">
            <h3 className="text-lg font-semibold">Company Contacts</h3>
            <Link
              href={`/dashboard/setup/company-contact/exhibitor-company/${companyId}/contacts/add`}
            >
              <Button variant="main" iconName="AddIcon">
                Add Contact
              </Button>
            </Link>
          </div>
        }
      >
        <ContactsTable companyId={companyId} companyName={company.name} />
      </CardGenerator>
    </div>
  );
}
