'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import { CategoryData, CategorySchema } from '@/schema/CategorySchema';
import CategoryQuery from '@/services/queries/CategoryQuery';
import { ChevronRight } from 'lucide-react';
import { DropzoneOptions } from 'react-dropzone';
import GroupQuery from '@/services/queries/GroupQuery';
import { getQueryClient } from '@/utils/query-client';

interface CategoryGeneralInfoProps {
  id?: number;
}

function FormContent({
  defaultValues,
  id,
}: {
  defaultValues?: CategoryData;
  id?: number;
}) {
  const { push } = useRouter();
  const { toast } = useToast();

  const dropzone = {
    accept: {
      'image/*': ['.jpg', '.jpeg', '.png'],
    },
    multiple: false,
    maxFiles: 1,
  } satisfies DropzoneOptions;

  const form = useForm<CategoryData>({
    resolver: zodResolver(CategorySchema),
    defaultValues: defaultValues ?? {
      groupId: undefined,
      name: undefined,
      code: undefined,
      displayOrder: undefined,
      isAvailable: true,
      isInternalProduct: false,
      isSoldByQ: false,
      image: undefined,
    },
  });

  const { data: group, isLoading: isLoadingGroup } = useQuery({
    queryKey: GroupQuery.tags,
    queryFn: GroupQuery.getAll,
    select: !id ? (res) => res.filter((item) => item.isAvailable) : undefined,
  });

  const { mutate, isPending } = useMutation({
    mutationFn: id
      ? (data: CategoryData) => CategoryQuery.update(id, data)
      : (data: CategoryData) => CategoryQuery.add(data),
    onSuccess: async (newId) => {
      toast({
        title: 'Success',
        description: id
          ? 'Category updated successfully.'
          : 'Category created successfully.',
        variant: 'success',
      });

      await getQueryClient().invalidateQueries({
        queryKey: ['Category', { id: Number(id) }],
      });

      await getQueryClient().invalidateQueries({
        queryKey: CategoryQuery.tags,
      });

      const categoryId = id || newId;

      if (categoryId) {
        push(
          `/dashboard/setup/products-services/category/${categoryId}/description`,
        );
      } else {
        push(`/dashboard/setup/products-services/category`);
      }
    },
  });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => mutate(data))}
        className="space-y-4"
      >
        <h2 className="text-xl font-semibold text-[#00646C] border-b border-slate-200 pb-3">
          Category Information
        </h2>
        <Field
          control={form.control}
          name="name"
          label="Category Name"
          placeholder="Enter category name"
          type="text"
          required
        />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-x-8 gap-y-2">
          {!isLoadingGroup && (
            <div className="md:col-span-2">
              <Field
                disabled={id ? true : false}
                control={form.control}
                name="groupId"
                label="Group"
                type={{
                  type: 'select',
                  props: {
                    placeholder: 'Select a group',
                    options:
                      group?.map((t) => ({
                        label: t.name ?? '',
                        value: String(t.id),
                      })) || [],
                  },
                }}
                required
              />
            </div>
          )}

          <div className="md:col-span-1">
            <Field
              control={form.control}
              name="displayOrder"
              label="Display Order"
              type="text"
            />
          </div>
        </div>

        <Field
          control={form.control}
          name="image"
          label="Category Image"
          required={id ? false : true}
          type={{
            type: 'file',
            props: {
              dropzoneOptions: dropzone,
            },
          }}
        />
        <Field
          control={form.control}
          name="isInternalProduct"
          label="Internal Product"
          type="checkbox"
        />
        <Field
          control={form.control}
          name="isSoldByQ"
          label="Sold by Quantity only?"
          type="checkbox"
        />
        <Field
          control={form.control}
          name="isAvailable"
          label="Available"
          type="checkbox"
        />
        <div className="flex justify-between pt-6 border-t border-slate-200">
          <Button
            variant="outline"
            type="button"
            onClick={() => push('/dashboard/setup/products-services/category')}
          >
            Cancel
          </Button>

          <Button variant="main" type="submit" disabled={isPending}>
            {isPending && <Spinner className="mr-2 text-white" />}
            Save & Continue
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default function CategoryGeneralInfo({ id }: CategoryGeneralInfoProps) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['Category', { id }],
    queryFn: () => CategoryQuery.getById(id!),
    enabled: !!id,
    select: (res): CategoryData => ({
      name: res.name ?? '',
      code: res.code ?? '',
      displayOrder: res.displayOrder?.toString() ?? '',
      isAvailable: res.isAvailable ?? false,
      isInternalProduct: res.isInternalProduct ?? false,
      isSoldByQ: res.isSoldByQ ?? false,
      imagePath: res.imagePath ?? '',
      image: res.image,
      groupId: res.groupId?.toString() ?? '',
    }),
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <FormContent defaultValues={data} id={id} />
    </Suspense>
  );
}
