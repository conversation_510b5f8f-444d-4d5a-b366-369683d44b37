// components/OfferingDisplayOut.tsx
'use client';
import { Link } from '@/utils/navigation';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { PropertyOptionsDto } from '@/models/Offering';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { ImageIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
interface OfferingDisplayProps {
  id: number;
  name?: string | null;
  groupId: number;
  categoryId: number;
  options?: PropertyOptionsDto[];
  code?: string | null;
  isActive?: boolean | null;
}

const OfferingDisplay: React.FC<OfferingDisplayProps> = ({
  id,
  name,
  groupId,
  categoryId,
  options,
  code,
  isActive,
}) => {
  return (
    <Accordion key={id} type="single" collapsible className="space-y-3">
      <AccordionItem value={name + id.toString()}>
        <AccordionTrigger
          className={`pl-3 py-1 hover:bg-slate-50 hover:rounded-lg`}
        >
          <div className="grid grid-cols-[1fr_80px_100px_80px] gap-2 items-center py-1 hover:bg-gray-50 w-full">
            <div className="flex items-center gap-1.5 cursor-pointer hover:text-main hover:underline">
              <span
                className={`text-sm font-medium truncate flex items-center gap-1 hover:text-main hover:underline ${options && options.length === 0 ? 'text-gray-500' : 'text-gray-900'} hover:text-main`}
              >
                {name}
              </span>
            </div>
            <div className="text-left -ml-[200px]">
              <span
                className="font-mono font-normal text-sm"
                style={{ color: '#B10055' }}
              >
                {code}
              </span>
            </div>
            <div className="grid grid-cols-[20px_30px_30px] gap-2 items-center -ml-[70px]">
              {/* Column 1: Image Icon */}
              <div className="flex justify-center">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="cursor-pointer">
                      <ImageIcon className="w-4 h-4 text-teal-600 hover:text-teal-700" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="right" className="p-2">
                    <div className="w-48 h-32">
                      {/* <ImageWithFallback
                        src={carpetSamplesImage}
                        alt={category.name}
                        className="w-full h-full object-cover rounded"
                      /> */}
                    </div>
                    <p className="text-sm mt-1 text-center">
                      {name} <span style={{ color: '#B10055' }}>{code}</span>
                    </p>
                  </TooltipContent>
                </Tooltip>
              </div>

              {/* Column 2: Edit Button */}
              <div className="flex justify-center">
                <Link
                  href={`/dashboard/setup/products-services/product/${groupId}/category/${categoryId}/${id}`}
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 px-1 py-0 hover:bg-slate-200 text-[#00646C]"
                    title="Edit Product"
                  >
                    <span className="text-sm">Edit</span>
                  </Button>
                </Link>
              </div>

              {/* Column 3: Add Button */}
              <div className="flex justify-center">
                <Link
                  href={`/dashboard/setup/products-services/product/${groupId}/category/${categoryId}/${id}/property`}
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 px-1 py-0 hover:bg-slate-200 text-[#00646C]"
                    title="Add new Variant to Product"
                  >
                    <span className="text-sm">Add</span>
                  </Button>
                </Link>
              </div>
            </div>
            <div className="-ml-[40px]">
              {isActive ? (
                <span className="text-[rgba(0,91,99,1)] text-sm font-normal">
                  Active
                </span>
              ) : (
                <span className="text-red-600 text-sm font-normal">
                  Discontinued
                </span>
              )}
            </div>
          </div>
        </AccordionTrigger>
        <AccordionContent className="ml-4 border-l border-gray-200 pb-0">
          {options && options.length > 0 ? (
            options.map((o, index) => (
              <div
                key={o.id}
                className={`ml-3 hover:no-underline hover:bg-slate-50 hover:rounded-lg`}
              >
                <div
                  className={`grid grid-cols-[1fr_80px_100px_80px] gap-2 items-center py-1 w-full ${
                    index < options.length - 1 ? 'border-b border-gray-200' : ''
                  }`}
                >
                  <div className="flex items-center gap-1.5 cursor-pointer hover:text-main hover:underline">
                    {/* <Link
                      href={`/dashboard/setup/products-services/product/${groupId}/category/${categoryId}/${id}/property`}
                    > */}
                    <span
                      className={`text-sm font-medium truncate flex items-center gap-1 hover:text-main hover:underline pl-2`}
                    >
                      {/* ({index + 1}) */}
                      {o.name}
                      {/* {o.image && (
                          <div className="relative inline-block group">
                            <div className="absolute bottom left-full ml-2 transform -translate-y-1/2 hidden group-hover:block z-50 bg-white border border-gray-300 rounded shadow-md">
                              <img
                                src={
                                  o.image.startsWith('/images')
                                    ? o.image
                                    : '/images' + o.image
                                }
                                alt="preview"
                                className="max-w-[100px] max-h-[100px] object-cover rounded"
                              />
                            </div>
                            <ImageIcon className="w-4 h-4" />
                          </div>
                        )} */}
                      {/* &nbsp;- {o.code} */}
                    </span>
                    {/* </Link> */}
                  </div>
                  <div className="text-left -ml-[200px]">
                    <span
                      className="font-mono font-normal text-sm"
                      style={{ color: '#B10055' }}
                    >
                      {o.code}
                    </span>
                  </div>
                  <div className="grid grid-cols-[20px_30px_30px] gap-2 items-center -ml-[70px]">
                    {/* Column 1: Image Icon */}
                    <div className="flex justify-center">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="cursor-pointer">
                            <ImageIcon className="w-4 h-4 text-teal-600 hover:text-teal-700" />
                          </div>
                        </TooltipTrigger>
                        <TooltipContent side="right" className="p-2">
                          <div className="w-48 h-32">
                            {/* <ImageWithFallback
                        src={carpetSamplesImage}
                        alt={category.name}
                        className="w-full h-full object-cover rounded"
                      /> */}
                          </div>
                          <p className="text-sm mt-1 text-center">
                            {o.name}{' '}
                            <span style={{ color: '#B10055' }}>{o.code}</span>
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </div>

                    {/* Column 2: Edit Button */}
                    <div className="flex justify-center">
                      <Link
                        href={`/dashboard/setup/products-services/product/${groupId}/category/${categoryId}/${id}/property`}
                      >
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-5 px-1 py-0 hover:bg-slate-200 text-[#00646C]"
                          title="Edit Property"
                        >
                          <span className="text-sm">Edit</span>
                        </Button>
                      </Link>
                    </div>
                    {/* Column 3: Add Button */}
                    <div className="flex justify-center"></div>
                  </div>
                  <div className="ml-0">
                    {o.isActive ? (
                      <span className="text-[rgba(0,91,99,1)] text-sm">
                        Active
                      </span>
                    ) : (
                      <span className="text-red-600 text-sm">Discontinued</span>
                    )}
                  </div>
                  {/* Property Product Status Column */}
                </div>
              </div>
            ))
          ) : (
            <div className="text-sm text-gray-500 italic">
              No options in this category.
            </div>
          )}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export default OfferingDisplay;
