'use client';

import { useQuery } from '@tanstack/react-query';
import { User } from 'lucide-react';
import Link from 'next/link';
import { DataTable } from '@/components/ui/data-table';
import { modal, DEFAULT_MODAL } from '@/components/ui/overlay';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { Button } from '@/components/ui/button';
import { ContactInList } from '@/models/Contact';
import ContactTypeQuery from '@/services/queries/ContactTypeQuery';
import { getQueryClient } from '@/utils/query-client';

interface ContactsTableProps {
  companyId: number;
  companyName: string;
}

export const ContactsTable = ({
  companyId,
  companyName,
}: ContactsTableProps) => {
  const { data, error, isLoading } = useQuery({
    queryKey: [...CompanyQuery.tags, 'contacts', companyId],
    queryFn: () => CompanyQuery.contacts.getAll(companyId),
  });

  const { data: contactTypes } = useQuery({
    queryKey: ContactTypeQuery.tags,
    queryFn: ContactTypeQuery.getAll,
  });

  const columns = generateTableColumns<ContactInList>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      fullName: { name: 'Full Name', type: 'text', sortable: true },
      contactType: { name: 'Contact Type', type: 'text', sortable: true },
      email: { name: 'Email', type: 'text', sortable: true },
      telephone: { name: 'Phone', type: 'text', sortable: true },
      cellphone: { name: 'Cell Phone', type: 'text', sortable: true },
      isArchived: {
        name: 'Status',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <span className="text-xs bg-gray-200 text-gray-700 px-1.5 py-0.5 rounded-full">
                  Archived
                </span>
              ) : (
                <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded-full">
                  Active
                </span>
              )}
            </div>
          ),
        },
        sortable: true,
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-1 w-full justify-center">
              <Link
                href={`/dashboard/setup/company-contact/show-company/${companyId}/contacts/${row.id}`}
              >
                <Button
                  size="sm"
                  variant="secondary"
                  iconName="EditIcon"
                ></Button>
              </Link>
              <Button
                variant="remove"
                size="sm"
                iconName="RemoveIcon"
                onClick={() => {
                  modal(
                    ({ close }) => (
                      <MutationConfirmModal
                        close={close}
                        title="Delete Contact"
                        description={`Are you sure you want to delete "${row.fullName}"?`}
                        mutateFn={() =>
                          CompanyQuery.contacts.delete(companyId, row.id)
                        }
                        mutationKey={[
                          ...CompanyQuery.tags,
                          'contacts',
                          companyId,
                        ]}
                        onSuccess={async () => {
                          await getQueryClient().invalidateQueries({
                            queryKey: [
                              ...CompanyQuery.tags,
                              'contacts',
                              companyId,
                            ],
                          });
                        }}
                        variant="destructive"
                        confirmButtonText="Delete"
                        confirmIconName="DeleteIcon"
                        loadingIconName="LoadingIcon"
                      />
                    ),
                    DEFAULT_MODAL,
                  ).open();
                }}
              ></Button>
            </div>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<ContactInList>({
    fullName: {
      name: 'Name',
      type: 'text',
    },
    contactType: {
      name: 'Contact Type',
      type: {
        type: 'select',
        options:
          contactTypes?.map((type) => ({
            label: type.name,
            value: type.name,
          })) || [],
      },
    },
  });

  if (error) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-sm font-medium text-brand-brown flex items-center gap-1">
            <User className="h-3 w-3" />
            Contacts for {companyName}
          </h3>
          <Link
            href={`/dashboard/setup/company-contact/show-company/${companyId}/contacts/add`}
          >
            <Button variant="main" iconName="AddIcon">
              Add Contact
            </Button>
          </Link>
        </div>
        <div className="text-red-500 p-4 text-center bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center justify-center space-x-2">
            <span className="text-red-600">⚠️</span>
            <span>Error loading contacts: {error.message}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <DataTable
      columns={columns}
      data={data || []}
      filterFields={filters}
      isLoading={isLoading}
      controls={
        <div className="flex flex-row gap-2 justify-end">
          <Link
            href={`/dashboard/setup/company-contact/show-company/${companyId}/contacts/add`}
          >
            <Button variant="main" iconName="AddIcon">
              Add Contact
            </Button>
          </Link>
        </div>
      }
    />
  );
};

export default ContactsTable;
