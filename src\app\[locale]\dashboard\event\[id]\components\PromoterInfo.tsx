import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { ShowPromoter } from '@/models/Show';

export default function PromoterInfo({
  promoter,
}: {
  promoter?: ShowPromoter;
}) {
  if (!promoter) {
    return null; // Or a loading/placeholder state
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Promoter Information</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div>
            <h3 className="font-medium text-slate-800 mb-2">
              Billed To Contact
            </h3>
            <div className="space-y-2">
              <div>
                <span className="font-medium text-slate-700">
                  Contact Name:
                </span>
                <span className="ml-2">{promoter.billedToContactName}</span>
              </div>
              <div>
                <span className="font-medium text-slate-700">Email:</span>
                <span className="ml-2">{promoter.billedToContactEmail}</span>
              </div>
              <div>
                <span className="font-medium text-slate-700">Company:</span>
                <span className="ml-2">{promoter.companyName}</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
