import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function PromoterInfo({ promoter }: { promoter: any }) {
  if (!promoter) {
    return null; // Or a loading/placeholder state
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Promoter Information</CardTitle>
      </CardHeader>
      <CardContent>
        <p>Name: {promoter.name}</p>
        <p>Email: {promoter.email}</p>
        <p>Phone: {promoter.phone}</p>
        {/* Add more promoter details as needed */}
      </CardContent>
    </Card>
  );
}