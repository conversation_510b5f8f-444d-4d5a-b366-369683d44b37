import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { ShowPromoter } from '@/models/Show';

export default function PromoterInfo({
  promoter,
}: {
  promoter?: ShowPromoter;
}) {
  if (!promoter) {
    return null; // Or a loading/placeholder state
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Promoter Information</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div>
            <span className="font-medium text-slate-700">Company:</span>
            <span className="ml-2">{promoter.companyName}</span>
          </div>
          <div>
            <span className="font-medium text-slate-700">Billed To:</span>
            <span className="ml-2">{promoter.billedToContactName}</span>
          </div>
          <div>
            <span className="font-medium text-slate-700">Email:</span>
            <span className="ml-2">{promoter.billedToContactEmail}</span>
          </div>
          <div>
            <span className="font-medium text-slate-700">Show Subcontact:</span>
            <span className="ml-2">
              {promoter.showSubcontact ? 'Yes' : 'No'}
            </span>
          </div>
          <div>
            <span className="font-medium text-slate-700">
              Floor Plan Required:
            </span>
            <span className="ml-2">
              {promoter.floorPlanRequired ? 'Yes' : 'No'}
            </span>
          </div>
          {promoter.taxes && promoter.taxes.length > 0 && (
            <div>
              <span className="font-medium text-slate-700">Taxes:</span>
              <div className="ml-2 mt-1">
                {promoter.taxes.map((tax) => (
                  <div key={tax.id} className="text-sm">
                    {tax.taxTypeName} ({tax.taxTypeAbbreviation}): {tax.taxRate}
                    % - {tax.provinceName}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
