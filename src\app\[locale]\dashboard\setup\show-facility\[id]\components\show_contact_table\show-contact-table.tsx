'use client';

import { useQuery } from '@tanstack/react-query';
import { CheckCircle, ChevronLeft, Edit2, XCircle } from 'lucide-react';
import { FaPlus } from 'react-icons/fa';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { modal, DEFAULT_MODAL } from '@/components/ui/overlay';
import ShowContactQuery from '@/services/queries/ShowContactQuery';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import { Contact } from '@/models/Contact';
import AddShowContactModal from '../add_show_contact_modal';
import { useRouter } from 'next/navigation';

interface ShowContactTableProps {
  locationId: number;
}

export const ShowContactTable = ({ locationId }: ShowContactTableProps) => {
  const { data, isLoading } = useQuery({
    queryKey: ['ShowContact', { locationId }],
    queryFn: () => ShowContactQuery.getByLocation(locationId),
    enabled: !!locationId,
  });

  const columns = generateTableColumns<Contact>(
    {
      id: { name: 'ID', type: 'text' },
      firstName: { name: 'First Name', type: 'text' },
      lastName: { name: 'Last Name', type: 'text' },
      email: { name: 'Email', type: 'text' },
      telephone: { name: 'Telephone', type: 'text' },
      cellphone: { name: 'Cellphone', type: 'text' },
      isArchived: {
        name: 'Active',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <XCircle className="text-red-600 w-4 h-4" />
              ) : (
                <CheckCircle className="text-green-600 w-4 h-4" />
              )}
            </div>
          ),
        },
        sortable: true,
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <Button
              size="sm"
              variant="secondary"
              iconName="EditIcon"
              onClick={() => {
                modal(
                  <AddShowContactModal
                    contactId={row.id}
                    locationId={locationId}
                  />,
                  {
                    ...DEFAULT_MODAL,
                    width: '25%',
                  },
                ).open();
              }}
            ></Button>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<Contact>({
    firstName: {
      name: 'First Name',
      type: 'text',
    },
    lastName: {
      name: 'Last Name',
      type: 'text',
    },
    email: {
      name: 'Email',
      type: 'text',
    },
    isArchived: {
      name: 'Active',
      type: {
        type: 'select',
        options: [
          { label: 'Active', value: 'false' },
          { label: 'Inactive', value: 'true' },
        ],
      },
    },
  });

  const { push } = useRouter();

  return (
    <div>
      <DataTable
        columns={columns}
        filterFields={filters}
        data={data}
        isLoading={isLoading}
        controls={
          <Button
            variant="primary"
            iconName="AddIcon"
            onClick={() => {
              modal(<AddShowContactModal locationId={locationId} />, {
                ...DEFAULT_MODAL,
                width: '25%',
              }).open();
            }}
          >
            Add New Contact
          </Button>
        }
      />
      <div className="flex justify-between pt-6 border-t border-slate-200 mt-3">
        <Button
          variant="outline"
          type="button"
          onClick={() => push('/dashboard/setup/show-facility')}
        >
          Cancel
        </Button>
        <Button
          variant="main"
          type="button"
          onClick={() => push(`/dashboard/setup/show-facility/${locationId}`)}
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Save & Back to Main
        </Button>
      </div>
    </div>
  );
};

export default ShowContactTable;
