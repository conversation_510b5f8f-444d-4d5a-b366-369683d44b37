import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  PorterServiceSchema,
  PorterServiceFormType,
} from '@/schema/ServiceFormSchemas';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';

interface PorterServiceFormProps {
  onSubmit: (data: PorterServiceFormType) => void;
  initialData?: Partial<PorterServiceFormType>;
  isLoading?: boolean;
}

export function PorterServiceForm({
  onSubmit,
  initialData,
  isLoading = false,
}: PorterServiceFormProps) {
  const form = useForm<PorterServiceFormType>({
    resolver: zodResolver(PorterServiceSchema),
    defaultValues: {
      minimumHours: initialData?.minimumHours || 2,
      minimumDays: initialData?.minimumDays || 1,
      minimumLabourers: initialData?.minimumLabourers || 1,
      rateType: initialData?.rateType || 'sq_ft',
      rates: initialData?.rates || [
        { rate: 2.0, exhibitArea: 100 },
        { rate: 1.75, exhibitArea: 200 },
        { rate: 1.5, exhibitArea: 300 },
        { rate: 1.0, exhibitArea: 400 },
      ],
    },
  });

  const { fields } = useFieldArray({
    control: form.control,
    name: 'rates',
  });

  const handleSubmit = (data: PorterServiceFormType) => {
    onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          <Field
            control={form.control}
            name="minimumHours"
            type="number"
            label="Minimum Hours"
            min="1"
          />

          <Field
            control={form.control}
            name="minimumDays"
            type="number"
            label="Minimum Days"
            min="1"
          />

          <Field
            control={form.control}
            name="minimumLabourers"
            type="number"
            label="Minimum Labourers"
            min="1"
          />
        </div>

        <Field
          control={form.control}
          name="rateType"
          type={{
            type: 'select',
            props: {
              options: [
                { label: 'sq. ft', value: 'sq_ft' },
                { label: 'Hourly', value: 'hourly' },
                { label: 'Daily', value: 'daily' },
              ],
              placeholder: 'Select rate type',
            },
          }}
          label="Rate Type"
          containerClassName="max-w-xs"
        />

        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-8">
            <div>
              <div className="text-sm font-medium">Rate</div>
            </div>
            <div>
              <div className="text-sm font-medium">Exhibit Area</div>
            </div>
          </div>

          {fields.map((field, index) => (
            <div key={field.id} className="grid grid-cols-2 gap-8">
              <div className="relative">
                <Field
                  control={form.control}
                  name={`rates.${index}.rate`}
                  type="number"
                  step="0.01"
                  min="0"
                />
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                  $
                </span>
              </div>

              <Field
                control={form.control}
                name={`rates.${index}.exhibitArea`}
                type="number"
                min="0"
              />
            </div>
          ))}
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
          <p className="text-sm text-yellow-800">
            <strong>Note:</strong> There are currently orders on this service.
            Rate cannot be modified.
          </p>
        </div>

        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Saving...' : 'Save Information'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
