import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  PorterServiceSchema,
  PorterServiceFormType,
} from '@/schema/ServiceFormSchemas';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface PorterServiceFormProps {
  onSubmit: (data: PorterServiceFormType) => void;
  initialData?: Partial<PorterServiceFormType>;
  isLoading?: boolean;
}

export function PorterServiceForm({
  onSubmit,
  initialData,
  isLoading = false,
}: PorterServiceFormProps) {
  const form = useForm<PorterServiceFormType>({
    resolver: zodResolver(PorterServiceSchema),
    defaultValues: {
      minimumHours: initialData?.minimumHours || 2,
      minimumDays: initialData?.minimumDays || 1,
      minimumLabourers: initialData?.minimumLabourers || 1,
      rateType: initialData?.rateType || 'sq_ft',
      rates: initialData?.rates || [
        { rate: 2.0, exhibitArea: 100 },
        { rate: 1.75, exhibitArea: 200 },
        { rate: 1.5, exhibitArea: 300 },
        { rate: 1.0, exhibitArea: 400 },
      ],
    },
  });

  const { fields } = useFieldArray({
    control: form.control,
    name: 'rates',
  });

  const handleSubmit = (data: PorterServiceFormType) => {
    onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="minimumHours"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Minimum Hours:</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min="1"
                    {...field}
                    onChange={(e) =>
                      field.onChange(parseInt(e.target.value) || 1)
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="minimumDays"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Minimum Days:</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min="1"
                    {...field}
                    onChange={(e) =>
                      field.onChange(parseInt(e.target.value) || 1)
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="minimumLabourers"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Minimum Labourers:</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min="1"
                    {...field}
                    onChange={(e) =>
                      field.onChange(parseInt(e.target.value) || 1)
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="rateType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Rate Type:</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="max-w-xs">
                    <SelectValue />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="sq_ft">sq. ft</SelectItem>
                  <SelectItem value="hourly">Hourly</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-8">
            <div>
              <FormLabel className="text-sm font-medium">Rate</FormLabel>
            </div>
            <div>
              <FormLabel className="text-sm font-medium">
                Exhibit Area
              </FormLabel>
            </div>
          </div>

          {fields.map((field, index) => (
            <div key={field.id} className="grid grid-cols-2 gap-8">
              <FormField
                control={form.control}
                name={`rates.${index}.rate`}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseFloat(e.target.value) || 0)
                          }
                        />
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                          $
                        </span>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`rates.${index}.exhibitArea`}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseInt(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          ))}
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
          <p className="text-sm text-yellow-800">
            <strong>Note:</strong> There are currently orders on this service.
            Rate cannot be modified.
          </p>
        </div>

        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Saving...' : 'Save Information'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
