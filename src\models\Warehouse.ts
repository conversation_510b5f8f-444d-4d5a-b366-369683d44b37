export interface WarehouseSummaryDto {
  id: number;
  code: string;
  phone: string;
  warehouseName: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  postalCode: string;
  provinceName: string;
  countryName: string;
  warehouseTypeName: string;
  contactPersonName: string;
  isActive: boolean;
}

export interface WarehouseDetailDto {
  warehouseId: number;
  code: string;
  phone: string;
  warehouseName: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  postalCode: string;
  provinceId: number;
  countryId: number;
  warehousetypeId: number;
  contactpersonId?: number;
  createdAt: string;
  createdById: number;
  updatedAt?: string;
  updatedById?: number;
  isActive?: boolean;
}
