'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { z } from 'zod';

import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Spinner } from '@/components/ui/spinner';
import ShowQuery from '@/services/queries/ShowQuery';
import { ShowPromoter, AvailableTax } from '@/models/Show';
import CompanyQuery from '@/services/queries/CompanyQuery'; // Assuming you have this
import ContactQuery from '@/services/queries/ContactQuery'; // And this

interface PromoterInfoTabProps {
  showId?: number;
  onSuccess?: () => void;
}

const PromoterInfoSchema = z.object({
  companyId: z.string().min(1, 'Company is required'),
  billedToContactId: z.string().min(1, 'Billing Contact is required'),
  showSubcontact: z.boolean().optional().default(false),
  floorPlanRequired: z.boolean().optional().default(false),
  selectedTaxes: z.record(z.boolean()).optional().default({}), // Object with taxId: boolean
});

function FormContent({
  defaultValues,
  showId,
  onSuccess,
}: {
  defaultValues?: any;
  showId?: number;
  onSuccess?: () => void;
}) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const isEditMode = !!showId;

  // --- Data Fetching ---
  const { data: promoterInfo, isLoading: isLoadingPromoter } = useQuery({
    queryKey: ['Shows', showId, 'promoter'],
    queryFn: () => ShowQuery.getPromoter(showId!),
    enabled: isEditMode,
  });

  const { data: availableTaxes, isLoading: isLoadingTaxes } = useQuery({
    queryKey: ['Shows', showId, 'availableTaxes'],
    queryFn: () => ShowQuery.getAvailableTaxes(showId!),
    enabled: isEditMode,
  });

  // Fetch companies (assuming you have a CompanyQuery)
  const { data: companies, isLoading: isLoadingCompanies } = useQuery({
    queryKey: ['companies'],
    queryFn: () => CompanyQuery.getAll(), // Replace with your actual query
  });

  // Initialize form with useForm
  const form = useForm<z.infer<typeof PromoterInfoSchema>>({
    resolver: zodResolver(PromoterInfoSchema),
    mode: 'onChange',
    defaultValues: {
      companyId: defaultValues?.companyId ? defaultValues.companyId.toString() : '',
      billedToContactId: defaultValues?.billedToContactId ? defaultValues.billedToContactId.toString() : '',
      showSubcontact: defaultValues?.showSubcontact ?? false,
      floorPlanRequired: defaultValues?.floorPlanRequired ?? false,
      selectedTaxes: defaultValues?.taxes?.reduce((acc: { [key: number]: boolean }, tax: any) => { // Assuming tax object has id
        acc[tax.taxId] = true;
        return acc;
      }, {}) ?? {},
    },
  });

  // --- Mutation for Updating ---
  const { mutate, isPending } = useMutation({
    mutationFn: async (data: z.infer<typeof PromoterInfoSchema>) => {
      const formattedData = {
        companyId: Number(data.companyId),
        billedToContactId: Number(data.billedToContactId),
        showSubcontact: data.showSubcontact,
        floorPlanRequired: data.floorPlanRequired,
        selectedTaxes: data.selectedTaxes,
      };

      await ShowQuery.setPromoter(showId!)(formattedData);
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Promoter information updated successfully',
      });
      queryClient.invalidateQueries({ queryKey: ['Shows', showId, 'promoter'] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update promoter information',
        variant: 'destructive',
      });
    },
  });

  // --- Derived Data for Form ---

  const companyOptions = companies
    ?.filter(company => company.groupTypeName === "Show manager") // only show companies with group type "Show manager"
    .map((company) => ({
      label: company.name,
      value: company.id.toString(),
    })) || [];

  const selectedCompanyId = form.watch("companyId");

  const { data: contacts, isLoading: isLoadingContacts } = useQuery({
    queryKey: ['contacts', selectedCompanyId],
    queryFn: () => ContactQuery.getByCompanyId(Number(selectedCompanyId)),  // Replace with your actual contact query by company
    enabled: !!selectedCompanyId,
  });

  const contactOptions = contacts?.map(contact => ({
    label: `${contact.firstName} ${contact.lastName}`,  // Or however you want to display contact name
    value: contact.id.toString(),
  })) || [];


  const isDataLoading = isLoadingPromoter || isLoadingTaxes || isLoadingCompanies || isLoadingContacts;

  // --- Rendering ---
  if (!isEditMode) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Promoter Information
          </h3>
          <p className="text-gray-500">
            Promoter information can only be set in edit mode. Please save the
            general information first.
          </p>
        </div>
      </div>
    );
  }

  if (isDataLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner />
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => mutate(data))}>
        <div className="space-y-6">
          <div>
            <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
              Promoter Information
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Field
                  control={form.control}
                  name="companyId"
                  label="Company"
                  type={{
                    type: 'select',
                    props: {
                      options: companyOptions,
                      placeholder: 'Select Company',
                    },
                  }}
                  required
                />
              </div>

              <div>
                <Field
                  control={form.control}
                  name="billedToContactId"
                  label="Billing Contact"
                  type={{
                    type: 'select',
                    props: {
                      options: contactOptions,
                      placeholder: "Select Billing Contact",
                      disabled: !selectedCompanyId
                    },
                  }}
                  required
                />
                {!selectedCompanyId && (
                  <p className="text-sm text-muted-foreground mt-1">Please select a company first to load contacts.</p>
                )}
              </div>
            </div>

            <div className="mt-4 space-y-2">
              <Field
                control={form.control}
                name="showSubcontact"
                label="Show Subcontact"
                type={{ type: 'checkbox' }}
              />
              <Field
                control={form.control}
                name="floorPlanRequired"
                label="Floor Plan Required"
                type={{ type: 'checkbox' }}
              />
            </div>

            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-700">Selected Taxes</h4>
              <div className="mt-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                {availableTaxes?.map((tax) => (
                  <label
                    key={tax.id}
                    className="flex items-center rounded-md border p-2 text-sm text-gray-700 hover:bg-gray-50 cursor-pointer"
                  >
                    <Field
                      control={form.control}
                      name={`selectedTaxes.${tax.id}`}
                      type={{ type: 'checkbox' }}
                    />
                    <span className="ml-2">{`${tax.taxTypeName} (${tax.taxRate}%)`}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={isPending}
              iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
              iconProps={{ size: 16, className: '' }}
            >
              {isPending ? 'Saving...' : 'Save & Continue'}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}

const PromoterInfoTab: React.FC<PromoterInfoTabProps> = ({
  showId,
  onSuccess,
}) => {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['Shows', showId, 'promoter'],
    queryFn: () => ShowQuery.getPromoter(showId!),
    enabled: !!showId,
  });

  if (isLoading && !isPaused) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner />
      </div>
    );
  }

  return (
    <FormContent defaultValues={data} showId={showId} onSuccess={onSuccess} />
  );
};

export default PromoterInfoTab;
