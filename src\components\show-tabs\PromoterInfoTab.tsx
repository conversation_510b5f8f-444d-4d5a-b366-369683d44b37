'use client';

import React from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { z } from 'zod';

import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import ShowQuery from '@/services/queries/ShowQuery';
import CompanyQuery from '@/services/queries/CompanyQuery';

interface PromoterInfoTabProps {
  showId?: number;
  onSuccess?: () => void;
}

const PromoterInfoSchema = z.object({
  companyId: z.string().min(1, 'Company is required'),
  billedToContactId: z.string().min(1, 'Billing Contact is required'),
  showSubcontact: z.boolean().optional().default(false),
  floorPlanRequired: z.boolean().optional().default(false),
  selectedTaxes: z.record(z.boolean().optional()).optional().default({}), // Object with taxId: boolean | undefined
});

function FormContent({
  showId,
  onSuccess,
  promoterData,
  availableTaxes,
  companies,
}: {
  showId?: number;
  onSuccess?: () => void;
  promoterData?: any;
  availableTaxes?: any[];
  companies?: any[];
}) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const isEditMode = !!showId;

  // Initialize form with useForm
  const form = useForm<z.infer<typeof PromoterInfoSchema>>({
    resolver: zodResolver(PromoterInfoSchema),
    mode: 'onChange',
    defaultValues: {
      companyId: promoterData?.companyId
        ? promoterData.companyId.toString()
        : '',
      billedToContactId: promoterData?.billedToContactId
        ? promoterData.billedToContactId.toString()
        : '',
      showSubcontact: promoterData?.showSubcontact ?? false,
      floorPlanRequired: promoterData?.floorPlanRequired ?? false,
      selectedTaxes: {},
    },
  });

  // Update form values when data is loaded
  React.useEffect(() => {
    if (availableTaxes && promoterData) {
      // Initialize all available taxes as false
      const taxes: { [key: string]: boolean } = {};

      availableTaxes.forEach((tax) => {
        taxes[tax.id.toString()] = false;
      });

      // Set the selected taxes to true
      promoterData.taxes?.forEach((tax: any) => {
        taxes[tax.taxId.toString()] = true;
      });

      form.setValue('selectedTaxes', taxes);
    } else if (availableTaxes) {
      // If no promoter data, initialize all as false
      const taxes: { [key: string]: boolean } = {};
      availableTaxes.forEach((tax) => {
        taxes[tax.id.toString()] = false;
      });
      form.setValue('selectedTaxes', taxes);
    }
  }, [availableTaxes, promoterData, form]);

  // --- Mutation for Updating ---
  const { mutate, isPending } = useMutation({
    mutationFn: async (data: z.infer<typeof PromoterInfoSchema>) => {
      // Convert selectedTaxes to the correct format
      const selectedTaxes: { [key: number]: boolean } = {};
      if (data.selectedTaxes) {
        Object.entries(data.selectedTaxes).forEach(([key, value]) => {
          selectedTaxes[Number(key)] = Boolean(value);
        });
      }

      const formattedData = {
        companyId: Number(data.companyId),
        billedToContactId: Number(data.billedToContactId),
        showSubcontact: data.showSubcontact || false,
        floorPlanRequired: data.floorPlanRequired || false,
        selectedTaxes,
      };

      await ShowQuery.setPromoter(showId!)(formattedData);
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Promoter information updated successfully',
      });
      queryClient.invalidateQueries({
        queryKey: ['Shows', showId, 'promoter'],
      });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update promoter information',
        variant: 'destructive',
      });
    },
  });

  // --- Derived Data for Form ---

  const companyOptions =
    companies
      ?.filter((company) => company.companyGroup === 'Show manager') // only show companies with group type "Show manager"
      .map((company) => ({
        label: company.name,
        value: company.id.toString(),
      })) || [];

  const selectedCompanyId = form.watch('companyId');

  const { data: contacts, isLoading: _isLoadingContacts } = useQuery({
    queryKey: ['Company', selectedCompanyId, 'contacts'],
    queryFn: () => CompanyQuery.contacts.getAll(Number(selectedCompanyId)),
    enabled: !!selectedCompanyId,
  });

  const contactOptions =
    contacts?.map((contact) => ({
      label: contact.fullName || `${contact.firstName} ${contact.lastName}`,
      value: contact.id.toString(),
    })) || [];

  // --- Rendering ---
  if (!isEditMode) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Promoter Information
          </h3>
          <p className="text-gray-500">
            Promoter information can only be set in edit mode. Please save the
            general information first.
          </p>
        </div>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => mutate(data))}>
        <div className="space-y-6">
          <div>
            <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
              Promoter Information
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Field
                  control={form.control}
                  name="companyId"
                  label="Company"
                  type={{
                    type: 'select',
                    props: {
                      options: companyOptions,
                      placeholder: 'Select Company',
                    },
                  }}
                  required
                />
              </div>

              <div>
                <Field
                  control={form.control}
                  name="billedToContactId"
                  label="Billing Contact"
                  type={{
                    type: 'select',
                    props: {
                      options: contactOptions,
                      placeholder: 'Select Billing Contact',
                    },
                  }}
                  disabled={!selectedCompanyId}
                  required
                />
                {!selectedCompanyId && (
                  <p className="text-sm text-muted-foreground mt-1">
                    Please select a company first to load contacts.
                  </p>
                )}
              </div>
            </div>

            <div className="mt-4 space-y-2">
              <Field
                control={form.control}
                name="showSubcontact"
                label="Show Subcontact"
                type="checkbox"
              />
              <Field
                control={form.control}
                name="floorPlanRequired"
                label="Floor Plan Required"
                type="checkbox"
              />
            </div>

            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-700">
                Selected Taxes
              </h4>
              <div className="mt-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                {availableTaxes?.map((tax) => (
                  <label
                    key={tax.id}
                    className="flex items-center rounded-md border p-2 text-sm text-gray-700 hover:bg-gray-50 cursor-pointer"
                  >
                    <Field
                      control={form.control}
                      name={`selectedTaxes.${tax.id}`}
                      type="checkbox"
                    />
                    <span className="ml-2">{`${tax.taxTypeName} (${tax.taxRate}%)`}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={isPending}
              iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
              iconProps={{ size: 16, className: '' }}
            >
              {isPending ? 'Saving...' : 'Save & Continue'}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}

const PromoterInfoTab: React.FC<PromoterInfoTabProps> = ({
  showId,
  onSuccess,
}) => {
  const isEditMode = !!showId;

  // --- Data Fetching ---
  const { data: promoterData, isLoading: isLoadingPromoter } = useQuery({
    queryKey: ['Shows', showId, 'promoter'],
    queryFn: () => ShowQuery.getPromoter(showId!),
    enabled: isEditMode,
  });

  const { data: availableTaxes, isLoading: isLoadingTaxes } = useQuery({
    queryKey: ['Shows', showId, 'availableTaxes'],
    queryFn: () => ShowQuery.getAvailableTaxes(showId!),
    enabled: isEditMode,
  });

  const { data: companies, isLoading: isLoadingCompanies } = useQuery({
    queryKey: ['companies'],
    queryFn: () => CompanyQuery.getAll(),
  });

  const isDataLoading =
    isLoadingPromoter || isLoadingTaxes || isLoadingCompanies;

  if (isDataLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner />
      </div>
    );
  }

  return (
    <FormContent
      showId={showId}
      onSuccess={onSuccess}
      promoterData={promoterData}
      availableTaxes={availableTaxes}
      companies={companies}
    />
  );
};

export default PromoterInfoTab;
