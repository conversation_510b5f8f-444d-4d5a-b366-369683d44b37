import { ShowLocationDetail, ShowLocationInList } from '@/models/ShowLocation'; // adjust path accordingly
import fetcher from './fetcher';
import {
  ShowLocationAddressData,
  ShowLocationGeneralData,
} from '@/schema/ShowLocationSchema';
import { urlToFile } from '@/utils/file-helper';

const ShowLocationQuery = {
  tags: ['ShowLocation'] as const,

  // GET: /ShowLocation
  getAll: async (): Promise<ShowLocationInList[]> => {
    return fetcher<ShowLocationInList[]>('ShowLocation');
  },

  getDetail: (id: number): Promise<ShowLocationInList> => {
    return fetcher<ShowLocationInList>(`ShowLocation/${id}/Details`);
  },

  // GET: /ShowLocation/GetById/{id}
  getById: async (id: number): Promise<ShowLocationDetail> => {
    return fetcher<ShowLocationDetail>(`ShowLocation/GetById/${id}`);
  },

  get: async (id: number): Promise<ShowLocationGeneralData> => {
    const data = await fetcher<ShowLocationDetail>(
      `ShowLocation/GetById/${id}`,
    );
    const filePath = '/doc' + String(data.accessPlanPath) + '?protected=true';
    const accessPlan = await urlToFile(filePath);
    return {
      id: data.id,
      locationCode: data.locationCode,
      name: data.name,
      telephone: data.telephone,
      mapLink: data.mapLink,
      website: data.website,
      fax: data.fax,
      tollfree: data.tollfree,
      email: data.email,
      accessPlanPath: data.accessPlanPath,
      accessPlan: data.accessPlanPath ? [accessPlan] : [],
      isArchived: data.isArchived,
    } as ShowLocationGeneralData;
  },

  // POST: /ShowLocation/general
  // createGeneral: async (data: ShowLocationGeneralData): Promise<boolean> => {
  //   return fetcher<boolean>('ShowLocation/general', {
  //     method: 'POST',
  //     headers: { 'Content-Type': 'application/json' },
  //     body: JSON.stringify(data),
  //   });
  // },

  addGeneral: async (data: ShowLocationGeneralData) => {
    const formData = new FormData();
    if (data.id) formData.append('id', data.id.toString());
    if (data.locationCode && data.locationCode != '')
      formData.append('locationCode', data.locationCode);
    if (data.name && data.name != '') formData.append('name', data.name);
    if (data.telephone && data.telephone != '')
      formData.append('telephone', data.telephone);
    if (data.mapLink && data.mapLink != '')
      formData.append('mapLink', data.mapLink);
    if (data.email && data.email != '') formData.append('email', data.email);
    if (data.website && data.website != '')
      formData.append('website', data.website);
    if (data.accessPlan) formData.append('accessPlan', data.accessPlan[0]);
    if (data.fax && data.fax != '') formData.append('fax', data.fax);
    if (data.tollfree && data.tollfree != '')
      formData.append('tollfree', data.tollfree);
    if (data.isArchived)
      formData.append('isArchived', data.isArchived.toString());
    return fetcher<number>(
      `ShowLocation/general`,
      {
        method: 'POST',
        body: formData,
      },
      true,
    );
  },

  // PATCH: /ShowLocation/general/{id}
  editGeneral: async (
    id: number,
    data: ShowLocationGeneralData,
  ): Promise<number> => {
    const formData = new FormData();
    if (data.id) formData.append('id', data.id.toString());
    if (data.locationCode && data.locationCode != '')
      formData.append('locationCode', data.locationCode);
    if (data.name && data.name != '') formData.append('name', data.name);
    if (data.telephone && data.telephone != '')
      formData.append('telephone', data.telephone);
    if (data.mapLink && data.mapLink != '')
      formData.append('mapLink', data.mapLink);
    if (data.email && data.email != '') formData.append('email', data.email);
    if (data.website && data.website != '')
      formData.append('website', data.website);
    if (data.accessPlan) formData.append('accessPlan', data.accessPlan[0]);
    if (data.fax && data.fax != '') formData.append('fax', data.fax);
    if (data.tollfree && data.tollfree != '')
      formData.append('tollfree', data.tollfree);
    if (data.isArchived)
      formData.append('isArchived', data.isArchived.toString());
    return fetcher<number>(
      `ShowLocation/general/${id}`,
      {
        method: 'PATCH',
        body: formData,
      },
      true,
    );
  },

  // PATCH: /ShowLocation/general/{id}
  // updateGeneral: async (
  //   id: number,
  //   data: ShowLocationGeneralData,
  // ): Promise<boolean> => {
  //   return fetcher<boolean>(`ShowLocation/general/${id}`, {
  //     method: 'PATCH',
  //     headers: { 'Content-Type': 'application/json' },
  //     body: JSON.stringify(data),
  //   });
  // },

  // GET: /ShowLocation/GetById/{id}
  getAddressById: async (id: number): Promise<ShowLocationAddressData> => {
    return fetcher<ShowLocationAddressData>(`ShowLocation/address/${id}`);
  },

  // PATCH: /ShowLocation/address/{id}
  updateAddress: async (
    id: number,
    data: ShowLocationAddressData,
  ): Promise<boolean> => {
    return fetcher<boolean>(`ShowLocation/address/${id}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
  },
};

export default ShowLocationQuery;
