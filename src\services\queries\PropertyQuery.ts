import fetcher from './fetcher';
import {
  PropertyCreateData,
  PropertyUpdateData,
} from '@/schema/PropertySchema';
import { Property } from '@/models/Property';

const PropertyQuery = {
  tags: ['Property'] as const,

  getAll: async () => fetcher<Property[]>('Property'),

  get: async (id: number) => fetcher<PropertyUpdateData>(`Property/${id}`),

  getDetail: (id: number): Promise<Property> => {
    return fetcher<Property>(`Category/${id}/Details`);
  },

  add: async (data: PropertyCreateData) =>
    fetcher<boolean>('Property', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  update: (id: number) => async (data: PropertyUpdateData) =>
    fetcher<boolean>(`Property/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),
};

export default PropertyQuery;
