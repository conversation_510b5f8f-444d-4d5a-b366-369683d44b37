// Base Service interface
export interface Service {
  id: number;
  name: string;
  description: string;
  serviceFormType: ServiceFormType;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Service form types enum
export enum ServiceFormType {
  GROUND_HANDLING = 'GROUND_HANDLING',
  ELECTRICAL = 'ELECTRICAL',
  PLUMBING = 'PLUMBING',
  CLEANING = 'CLEANING',
  SECURITY = 'SECURITY',
  CATERING = 'CATERING',
  AUDIO_VISUAL = 'AUDIO_VISUAL',
  DECORATION = 'DECORATION',
  TRANSPORTATION = 'TRANSPORTATION',
  STORAGE = 'STORAGE',
}

// Show Service Selection interface
export interface ShowService {
  id: number;
  showId: number;
  serviceId: number;
  isSelected: boolean;
  formData?: ServiceFormData;
  createdAt: string;
  updatedAt: string;
  // Populated fields
  service: Service;
}

// Base interface for all service form data
export interface ServiceFormData {
  serviceId: number;
  showId: number;
  formType: ServiceFormType;
  data: Record<string, any>;
}

// Ground Handling Service Form Data
export interface GroundHandlingFormData {
  equipmentRequired: string[];
  numberOfWorkers: number;
  workingHours: {
    startTime: string;
    endTime: string;
  };
  specialRequirements: string;
  contactPerson: string;
  contactPhone: string;
  estimatedCost: number;
}

// Electrical Service Form Data
export interface ElectricalFormData {
  powerRequirement: number; // in watts
  numberOfOutlets: number;
  voltageType: '110V' | '220V' | '440V';
  specialEquipment: string[];
  installationDate: string;
  removalDate: string;
  electricianRequired: boolean;
  safetyRequirements: string;
  estimatedCost: number;
}

// Plumbing Service Form Data
export interface PlumbingFormData {
  waterSupplyRequired: boolean;
  drainageRequired: boolean;
  numberOfFixtures: number;
  fixtureTypes: string[];
  installationDate: string;
  removalDate: string;
  plumberRequired: boolean;
  specialRequirements: string;
  estimatedCost: number;
}

// Cleaning Service Form Data
export interface CleaningFormData {
  cleaningType: 'DAILY' | 'WEEKLY' | 'EVENT_BASED' | 'DEEP_CLEANING';
  areasToClean: string[];
  frequency: string;
  specialRequirements: string;
  cleaningSupplies: string[];
  numberOfCleaners: number;
  workingHours: {
    startTime: string;
    endTime: string;
  };
  estimatedCost: number;
}

// Security Service Form Data
export interface SecurityFormData {
  securityType: 'GUARDS' | 'CAMERAS' | 'ACCESS_CONTROL' | 'ALARM_SYSTEM';
  numberOfGuards: number;
  coverageAreas: string[];
  workingHours: {
    startTime: string;
    endTime: string;
  };
  specialRequirements: string;
  equipmentNeeded: string[];
  emergencyContact: string;
  estimatedCost: number;
}

// Catering Service Form Data
export interface CateringFormData {
  mealType: 'BREAKFAST' | 'LUNCH' | 'DINNER' | 'SNACKS' | 'BEVERAGES';
  numberOfPeople: number;
  dietaryRestrictions: string[];
  menuPreferences: string;
  servingDate: string;
  servingTime: string;
  setupRequirements: string;
  specialRequirements: string;
  estimatedCost: number;
}

// Audio Visual Service Form Data
export interface AudioVisualFormData {
  equipmentType: string[];
  soundSystemRequired: boolean;
  lightingRequired: boolean;
  projectionRequired: boolean;
  recordingRequired: boolean;
  setupDate: string;
  eventDate: string;
  removalDate: string;
  technicianRequired: boolean;
  specialRequirements: string;
  estimatedCost: number;
}

// Decoration Service Form Data
export interface DecorationFormData {
  decorationType: string[];
  theme: string;
  colorScheme: string[];
  setupDate: string;
  eventDate: string;
  removalDate: string;
  specialRequirements: string;
  materialsProvided: boolean;
  designerRequired: boolean;
  estimatedCost: number;
}

// Transportation Service Form Data
export interface TransportationFormData {
  vehicleType: string[];
  numberOfVehicles: number;
  pickupLocation: string;
  dropoffLocation: string;
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  driverRequired: boolean;
  specialRequirements: string;
  estimatedCost: number;
}

// Storage Service Form Data
export interface StorageFormData {
  storageType: 'TEMPORARY' | 'LONG_TERM' | 'CLIMATE_CONTROLLED';
  storageSize: string;
  storageDuration: {
    startDate: string;
    endDate: string;
  };
  itemsToStore: string[];
  accessRequirements: string;
  securityLevel: 'BASIC' | 'MEDIUM' | 'HIGH';
  specialRequirements: string;
  estimatedCost: number;
}

// Union type for all form data types
export type AllServiceFormData = 
  | GroundHandlingFormData
  | ElectricalFormData
  | PlumbingFormData
  | CleaningFormData
  | SecurityFormData
  | CateringFormData
  | AudioVisualFormData
  | DecorationFormData
  | TransportationFormData
  | StorageFormData;

// Request interfaces
export interface CreateShowServiceRequest {
  showId: number;
  serviceId: number;
  isSelected: boolean;
  formData?: ServiceFormData;
}

export interface UpdateShowServiceRequest {
  isSelected: boolean;
  formData?: ServiceFormData;
}
