'use client';

import { useState, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  CalendarIcon,
  MapPinIcon,
  SearchIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@/assets/Icons';
import { useRouter } from 'next/navigation';
import { ShowInList } from '@/models/Show';
import { Province } from '@/models/Province';
import { ShowLocationInList } from '@/models/ShowLocation';
import ShowQuery from '@/services/queries/ShowQuery';
import { ProvinceQuery } from '@/services/queries/ProvinceQuery';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';

interface Event {
  id: number;
  name: string;
  startDate: string;
  endDate: string;
  location: string;
  province: string;
  displayStatus: boolean;
  createdBy: string;
}

export default function EventsTable() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [provinceFilter, setProvinceFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [locationFilter, setLocationFilter] = useState<string>('all');

  // Fetch shows data
  const {
    data: shows,
    isLoading: isShowsLoading,
    isError: isShowsError,
    error: showsError,
  } = useQuery<ShowInList[], Error>({
    queryKey: ShowQuery.tags,
    queryFn: ShowQuery.getAll,
    retry: 2,
  });

  // Fetch provinces data
  const {
    data: provincesData,
    isLoading: isProvincesLoading,
    isError: isProvincesError,
    error: provincesError,
  } = useQuery<Province[], Error>({
    queryKey: ProvinceQuery.tags,
    queryFn: ProvinceQuery.getAll,
    staleTime: 60_000, // Cache for 1 minute since provinces don't change often
    retry: 1,
  });

  // Fetch locations data
  const {
    data: locationsData,
    isLoading: isLocationsLoading,
    isError: isLocationsError,
    error: locationsError,
  } = useQuery<ShowLocationInList[], Error>({
    queryKey: ShowLocationQuery.tags,
    queryFn: ShowLocationQuery.getAll,
    staleTime: 60_000, // Cache for 1 minute since locations don't change often
    retry: 1,
  });

  // Transform API data to Event format
  const eventsData: Event[] = useMemo(() => {
    if (!shows || !provincesData || !locationsData) return [];

    return shows.map((show) => {
      // Find province name by ID
      const province = provincesData.find((p) => p.id === show.provinceId);
      // Find location name by ID
      const location = locationsData.find((l) => l.id === show.locationId);

      return {
        id: show.id,
        name: show.name,
        startDate: show.startDate,
        endDate: show.endDate,
        location: location?.name || 'Unknown Location',
        province: province?.name || 'Unknown Province',
        displayStatus: show.display,
        createdBy: show.createdByUsername || 'Unknown',
      };
    });
  }, [shows, provincesData, locationsData]);

  const provinces = useMemo(() => {
    if (!provincesData) return [{ value: 'all', label: 'All Provinces' }];

    // Remove duplicates by name and sort
    const uniqueProvinces = provincesData
      .reduce((acc, current) => {
        const existing = acc.find((item) => item.name === current.name);
        if (!existing) {
          acc.push(current);
        }
        return acc;
      }, [] as Province[])
      .sort((a, b) => a.name.localeCompare(b.name));

    return [
      { value: 'all', label: 'All Provinces' },
      ...uniqueProvinces.map((p) => ({ value: p.name, label: p.name })),
    ];
  }, [provincesData]);

  const locations = useMemo(() => {
    if (!locationsData) return [{ value: 'all', label: 'All Locations' }];

    // Remove duplicates by name and sort
    const uniqueLocations = locationsData
      .filter((l) => l.name) // Filter out locations without names
      .reduce((acc, current) => {
        const existing = acc.find((item) => item.name === current.name);
        if (!existing) {
          acc.push(current);
        }
        return acc;
      }, [] as ShowLocationInList[])
      .sort((a, b) => (a.name || '').localeCompare(b.name || ''));

    return [
      { value: 'all', label: 'All Locations' },
      ...uniqueLocations.map((l) => ({
        value: l.name || '',
        label: l.name || '',
      })),
    ];
  }, [locationsData]);

  const filteredEvents = useMemo(() => {
    return eventsData.filter((event) => {
      const searchMatch =
        searchTerm === '' ||
        event.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.province.toLowerCase().includes(searchTerm.toLowerCase());

      const provinceMatch =
        provinceFilter === 'all' || event.province === provinceFilter;

      const locationMatch =
        locationFilter === 'all' || event.location === locationFilter;

      const statusMatch =
        statusFilter === 'all' ||
        (statusFilter === 'active' && event.displayStatus) ||
        (statusFilter === 'inactive' && !event.displayStatus);

      return searchMatch && provinceMatch && locationMatch && statusMatch;
    });
  }, [eventsData, searchTerm, provinceFilter, locationFilter, statusFilter]);

  const activeFilterCount = [
    searchTerm && searchTerm.trim() !== '',
    provinceFilter !== 'all',
    locationFilter !== 'all',
    statusFilter !== 'all',
  ].filter(Boolean).length;

  const resetFilters = () => {
    setSearchTerm('');
    setProvinceFilter('all');
    setLocationFilter('all');
    setStatusFilter('all');
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}/${date.getFullYear()}`;
  };

  const getDisplayStatusBadge = (isActive: boolean) => {
    if (isActive) {
      return (
        <span className="text-[#CED600] font-medium flex items-center">
          <CheckCircleIcon className="h-4 w-4 mr-1 text-[#CED600]" />
          Active
        </span>
      );
    } else {
      return (
        <span className="text-[#77400F] font-medium flex items-center">
          <XCircleIcon className="h-4 w-4 mr-1 text-[#77400F]" />
          Inactive
        </span>
      );
    }
  };

  const handleViewEvent = (eventId: number) => {
    router.push(`/dashboard/event/${eventId}`);
  };

  // Loading state
  if (isShowsLoading || isProvincesLoading || isLocationsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00646C] mx-auto mb-4"></div>
          <p className="text-slate-600">Loading events...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (isShowsError || isProvincesError || isLocationsError) {
    const errorMessage =
      showsError?.message ||
      provincesError?.message ||
      locationsError?.message ||
      'Failed to load data';

    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <XCircleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 font-medium mb-2">Error Loading Events</p>
          <p className="text-slate-600 text-sm">{errorMessage}</p>
          <Button
            variant="outline"
            size="sm"
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
        <div className="relative w-full md:w-64">
          <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-400" />
          <Input
            type="text"
            placeholder="Search events..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex flex-wrap gap-2 w-full md:w-auto">
          <Select
            value={provinceFilter}
            onValueChange={setProvinceFilter}
            disabled={isProvincesLoading || !provincesData}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All Provinces" />
            </SelectTrigger>
            <SelectContent>
              {provinces.map((province) => (
                <SelectItem key={province.value} value={province.value}>
                  {province.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={locationFilter}
            onValueChange={setLocationFilter}
            disabled={isLocationsLoading || !locationsData}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All Locations" />
            </SelectTrigger>
            <SelectContent>
              {locations.map((location) => (
                <SelectItem key={location.value} value={location.value}>
                  {location.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All Statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>

          {activeFilterCount > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={resetFilters}
              className="flex items-center gap-1"
            >
              <XCircleIcon className="h-3 w-3" />
              Reset ({activeFilterCount})
            </Button>
          )}
        </div>
      </div>

      {/* Custom table with sticky header */}
      <div className="border rounded-md bg-white">
        <div className="overflow-auto max-h-[70vh] relative">
          {/* Sticky header */}
          <div className="sticky top-0 z-20 bg-slate-50 border-b">
            <div className="grid grid-cols-5 w-full">
              <div className="font-medium p-4">Event Name</div>
              <div className="font-medium p-4">Date</div>
              <div className="font-medium p-4">Location</div>
              <div className="font-medium p-4">Province</div>
              <div className="font-medium p-4">Status</div>
              {/* <div className="font-medium p-4">Created By</div> */}
            </div>
          </div>

          {/* Table body */}
          <div>
            {filteredEvents.length === 0 ? (
              <div className="p-8 text-center text-slate-500">
                {eventsData.length === 0
                  ? 'No events available.'
                  : 'No events match your filters.'}
              </div>
            ) : (
              filteredEvents.map((event, index) => (
                <div
                  key={event.id}
                  className={`grid grid-cols-5 w-full ${index % 2 === 1 ? 'bg-slate-50' : ''}`}
                >
                  <div className="p-4 font-medium text-[#00646C]">
                    <button
                      className="text-left hover:underline focus:outline-none focus:underline"
                      onClick={() => handleViewEvent(event.id)}
                    >
                      {event.name}
                    </button>
                  </div>
                  <div className="p-4">
                    <div className="flex items-center gap-1">
                      <CalendarIcon className="h-3.5 w-3.5 text-slate-400" />
                      <span>
                        {formatDate(event.startDate)} -{' '}
                        {formatDate(event.endDate)}
                      </span>
                    </div>
                  </div>
                  <div className="p-4">
                    <div className="flex items-center gap-1">
                      <MapPinIcon className="h-3.5 w-3.5 text-slate-400" />
                      <span>{event.location}</span>
                    </div>
                  </div>
                  <div className="p-4">{event.province}</div>
                  <div className="p-4">
                    {getDisplayStatusBadge(event.displayStatus)}
                  </div>
                  {/* <div className="p-4 text-slate-600">{event.createdBy}</div> */}
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
