'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Spinner } from '@/components/ui/spinner';
import { useToast } from '@/components/ui/use-toast';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { modal } from '@/components/ui/overlay';
import Suspense from '@/components/ui/Suspense';
import { getQueryClient } from '@/utils/query-client';
import Field from '@/components/ui/inputs/field';
import WarehouseQuery from '@/services/queries/WarehouseQuery';
import { ProvinceQuery } from '@/services/queries/ProvinceQuery';
import { CountryQuery } from '@/services/queries/CountryQuery';
import { WarehouseData, WarehouseSchema } from '@/schema/WarehouseSchema';
import { WarehouseDetailDto } from '@/models/Warehouse';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

function FormContent({
  defaultValues,
  id,
}: {
  defaultValues?: WarehouseData;
  id?: number;
}) {
  const { toast } = useToast();
  const { push } = useRouter();
  const form = useForm<WarehouseData>({
    resolver: zodResolver(WarehouseSchema),
    defaultValues: {
      code: defaultValues?.code ?? '',
      warehouseName: defaultValues?.warehouseName ?? '',
      addressLine1: defaultValues?.addressLine1 ?? '',
      addressLine2: defaultValues?.addressLine2 ?? '',
      city: defaultValues?.city ?? '',
      postalCode: defaultValues?.postalCode ?? '',
      provinceId: defaultValues?.provinceId?.toString() ?? undefined,
      countryId: defaultValues?.countryId?.toString() ?? undefined,
      warehouseTypeId: defaultValues?.warehouseTypeId?.toString() ?? '1',
      contactPersonId: defaultValues?.contactPersonId?.toString() ?? undefined,
      phone: defaultValues?.phone ?? '',
      isActive: defaultValues?.isActive ?? true,
    },
  });

  const { data: provinces, isLoading: isLoadingProvinces } = useQuery({
    queryKey: ProvinceQuery.tags,
    queryFn: ProvinceQuery.getAll,
  });

  const { data: countries, isLoading: isLoadingCountries } = useQuery({
    queryKey: CountryQuery.tags,
    queryFn: CountryQuery.getAll,
  });

  const { mutate, isPending } = useMutation({
    mutationFn: id ? WarehouseQuery.update(id) : WarehouseQuery.add,
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({
        queryKey: [WarehouseQuery.tags, { warehouseType: 1 }],
      });
      if (id) {
        await getQueryClient().invalidateQueries({
          queryKey: ['Warehouse Info', { id }],
        });
      }
      toast({
        title: 'Success',
        description: id
          ? 'Warehouse updated successfully'
          : 'New warehouse created',
        variant: 'success',
      });
      push('/dashboard/setup/master-setup/advance-receiving-warehouse');
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to create warehouse',
        variant: 'destructive',
      });
    },
  });

  useEffect(() => {
    const selectedProvinceId = form.getValues('provinceId');

    if (selectedProvinceId && provinces) {
      const matchedProvince = provinces.find(
        (p) => p.id.toString() === selectedProvinceId,
      );

      if (matchedProvince?.countryId) {
        const matchedCountryId = matchedProvince.countryId.toString();
        if (form.getValues('countryId') !== matchedCountryId) {
          form.setValue('countryId', matchedCountryId);
        }
      }
    }
  }, [form.watch('provinceId')]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => mutate(data))}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="md:col-span-2">
            <Field
              control={form.control}
              name="warehouseName"
              label="Warehouse Name"
              type="text"
              required
            />
          </div>
          <div className="md:col-span-1">
            <Field
              control={form.control}
              name="code"
              label="Code"
              type="text"
              disabled
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-32">
          {/* <Field
            control={form.control}
            name="warehouseTypeId"
            label="Warehouse Type"
            required
            type={{
              type: 'select',
              props: {
                options:
                  types?.map((type) => ({
                    label: type.name,
                    value: type.id.toString(),
                  })) ?? [],
                placeholder: 'Select a Warehouse Type',
              },
            }}
          /> */}
          <Field
            control={form.control}
            name="addressLine1"
            label="Address Line 1"
            type="text"
          />
          <Field
            control={form.control}
            name="addressLine2"
            label="Address Line 2"
            type="text"
          />
          <Field control={form.control} name="city" label="City" type="text" />
          <Field
            control={form.control}
            name="postalCode"
            label="Postal Code"
            type="text"
          />
          {isLoadingProvinces ? (
            <Spinner />
          ) : (
            <Field
              control={form.control}
              name="provinceId"
              label="Province"
              required
              type={{
                type: 'select',
                props: {
                  options:
                    provinces
                      ?.sort((a, b) => a.countryId - b.countryId)
                      .map((p) => ({
                        label: p.name,
                        value: p.id.toString(),
                      })) ?? [],
                  placeholder: 'Select Province',
                },
              }}
            />
          )}
          {isLoadingCountries ? (
            <Spinner />
          ) : (
            <Field
              control={form.control}
              name="countryId"
              label="Country"
              required
              type={{
                type: 'select',
                props: {
                  options:
                    (form.watch('provinceId') == undefined
                      ? (countries ?? [])
                      : countries &&
                        countries.filter(
                          (c) => c.id.toString() === form.watch('countryId'),
                        )
                    )?.map((b) => ({
                      value: b.id.toString(),
                      label: b.name,
                    })) ?? [],
                  placeholder: 'Select a country',
                },
              }}
            />
          )}
          <Field
            control={form.control}
            name="phone"
            label="Phone"
            type="phone"
          />
          {/* <Field
            control={form.control}
            name="contactPersonId"
            label="Contact Person"
            type={{
              type: 'select',
              props: {
                options:
                  users?.map((person) => ({
                    label: person.name,
                    value: person.id.toString(),
                  })) ?? [],
                placeholder: 'Select Contact Person',
              },
            }}
          /> */}
        </div>
        <Field
          control={form.control}
          name="isActive"
          label="Active"
          type="checkbox"
        />
        <div className="flex justify-between pt-6 border-t border-slate-200">
          <Button
            type="button"
            variant="outline"
            onClick={() =>
              push('/dashboard/setup/master-setup/advance-receiving-warehouse')
            }
          >
            Cancel
          </Button>
          <Button
            variant={'main'}
            disabled={isPending}
            iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
            iconProps={{ className: isPending ? 'animate-spin' : '' }}
            type="submit"
          >
            {isPending ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </form>
    </Form>
  );
}

interface AdvanceReceivingWarehouseModalProps {
  id?: number;
}

function WarehouseForm({ id }: AdvanceReceivingWarehouseModalProps) {
  const {
    data: warehouse,
    isPaused,
    isLoading,
  } = useQuery({
    queryKey: ['Warehouse Info', { id }],
    queryFn: () => WarehouseQuery.get(Number(id!)),
    enabled: !!id,
    select: (data: WarehouseDetailDto) => {
      return {
        code: data.code,
        warehouseName: data.warehouseName ?? '',
        addressLine1: data.addressLine1 ?? null,
        addressLine2: data.addressLine2 ?? null,
        city: data.city ?? null,
        postalCode: data.postalCode ?? null,
        provinceId: data.provinceId?.toString() ?? undefined,
        countryId: data.countryId?.toString() ?? undefined,
        warehouseTypeId: data.warehousetypeId?.toString() ?? undefined,
        contactPersonId: data.contactpersonId?.toString() ?? undefined,
        phone: data.phone.toString() ?? null,
        isActive: data.isActive ?? false,
      };
    },
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      {isLoading && isPaused ? (
        <Spinner />
      ) : (
        <FormContent
          defaultValues={warehouse}
          id={warehouse ? Number(id) : undefined}
        />
      )}
    </Suspense>
  );
}

export default WarehouseForm;
