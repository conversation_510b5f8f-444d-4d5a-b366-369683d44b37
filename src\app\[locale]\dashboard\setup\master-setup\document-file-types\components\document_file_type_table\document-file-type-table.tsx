'use client';

import { useQuery } from '@tanstack/react-query';
import { DataTable } from '@/components/ui/data-table';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import DocumentFileTypeQuery from '@/services/queries/DocumentFileTypeQuery';
import { Button } from '@/components/ui/button';
import { DocumentFileTypeInList } from '@/models/DocumentFileType';
import { Link } from '@/utils/navigation';
import { Badge } from '@/components/ui/Badge';
import { getQueryClient } from '@/utils/query-client';
import { useToast } from '@/components/ui/use-toast';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';

export default function DocumentFileTypeTable() {
  const { data: documentFileTypes, isLoading } = useQuery({
    queryKey: DocumentFileTypeQuery.tags,
    queryFn: () => DocumentFileTypeQuery.getAll(),
  });
  const { toast } = useToast();

  const columns = generateTableColumns<DocumentFileTypeInList>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      extensionCode: { name: 'Extension Code', type: 'text', sortable: true },
      extension: { name: 'Extension', type: 'text', sortable: true },
      isImage: {
        name: 'Is Image',
        type: {
          type: 'node',
          render: ({ row }) => (
            <Badge variant={row.isImage ? 'default' : 'secondary'}>
              {row.isImage ? 'Yes' : 'No'}
            </Badge>
          ),
        },
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-2 ">
              <Link
                href={`/dashboard/setup/master-setup/document-file-types/${row.id ?? 'add'}`}
              >
                <Button
                  size="sm"
                  variant="secondary"
                  iconName="EditIcon"
                ></Button>
              </Link>
              <Button
                variant="remove"
                size="sm"
                iconName="RemoveIcon"
                onClick={() => {
                  modal(
                    ({ close }) => (
                      <MutationConfirmModal
                        close={close}
                        title="Delete Document File Type"
                        description={`Are you sure you want to delete "${row.name}"? This action cannot be undone.`}
                        mutateFn={async () =>
                          DocumentFileTypeQuery.delete(row.id!)
                        }
                        mutationKey={DocumentFileTypeQuery.tags}
                        onSuccess={async () => {
                          await getQueryClient().invalidateQueries({
                            queryKey: DocumentFileTypeQuery.tags,
                          });
                          toast({
                            title: 'Document file type deleted',
                            variant: 'success',
                          });
                        }}
                        onError={(e: Error) => {
                          toast({
                            title:
                              e.message ||
                              'Failed to delete document file type',
                            variant: 'destructive',
                          });
                        }}
                        variant="destructive"
                        confirmButtonText="Delete"
                        confirmIconName="DeleteIcon"
                        loadingIconName="LoadingIcon"
                      />
                    ),
                    DEFAULT_MODAL,
                  ).open();
                }}
              ></Button>
            </div>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<DocumentFileTypeInList>({
    name: { name: 'Name', type: 'text' },
    extensionCode: { name: 'Extension Code', type: 'text' },
    extension: { name: 'Extension', type: 'text' },
    isImage: {
      name: 'Is Image',
      type: {
        type: 'select',
        options: [
          { label: 'All', value: '' },
          { label: 'Yes', value: 'true' },
          { label: 'No', value: 'false' },
        ],
      },
    },
  });

  return (
    <>
      <DataTable
        columns={columns}
        data={documentFileTypes}
        filterFields={filters}
        isLoading={isLoading}
        controls={
          <Link href="/dashboard/setup/master-setup/document-file-types/add">
            <Button
              variant={'main'}
              iconName="AddIcon"
              iconProps={{ className: 'text-white' }}
            >
              Add Warehouse
            </Button>
          </Link>
        }
      />
    </>
  );
}
