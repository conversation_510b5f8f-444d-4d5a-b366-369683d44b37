import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

export default function HallContactInfo({ hallContact }: { hallContact: any }) {
  if (!hallContact) {
    return null; // Or a loading/placeholder state
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Hall Contact Information</CardTitle>
      </CardHeader>
      <CardContent>
        <p>Hall Name: {hallContact.hallName}</p>
        <p>Contact Name: {hallContact.contactName}</p>
        <p>Contact Email: {hallContact.contactEmail}</p>
        <p>Contact Phone: {hallContact.contactPhone}</p>
        {/* Add more hall contact details as needed */}
      </CardContent>
    </Card>
  );
}