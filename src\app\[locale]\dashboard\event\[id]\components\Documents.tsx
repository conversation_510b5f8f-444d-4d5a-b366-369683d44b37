import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download, FileText } from 'lucide-react';
import UnderDevelopment from './UnderDevelopment';
import { ShowGeneralInfoData } from '@/app/[locale]/dashboard/setup/list-of-shows/components/show-tabs/GeneralInfoTab';

export default function Documents({ show }: { show?: ShowGeneralInfoData }) {
  if (!show || !show.documents) {
    return <UnderDevelopment sectionName="Show Documents Available" />;
  }

  return (
    <div className="mb-6">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Show Documents Available:</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {show.documents.map((document: any) => (
              <Button
                key={document}
                variant="outline"
                className="w-full justify-start"
              >
                <FileText className="mr-2 h-4 w-4" />
                {document}
                <Download className="ml-auto h-4 w-4" />
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
