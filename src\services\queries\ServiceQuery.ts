import {
  Service,
  ShowService,
  CreateShowServiceRequest,
  UpdateShowServiceRequest,
  ServiceFormType,
} from '@/models/Service';

// Mock data for services
const mockServices: Service[] = [
  {
    id: 1,
    name: 'Janitorial Services',
    description: 'Professional cleaning services for your event space',
    serviceFormType: ServiceFormType.JANITORIAL_SERVICES,
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 2,
    name: 'Labour (Installation and Dismantle)',
    description: 'Professional labour for setup and breakdown',
    serviceFormType: ServiceFormType.LABOUR_INSTALLATION_DISMANTLE,
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 3,
    name: 'On-Site Material Handling',
    description: 'Material handling and logistics services',
    serviceFormType: ServiceFormType.ON_SITE_MATERIAL_HANDLING,
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 4,
    name: 'Porter Service',
    description: 'Professional porter services for your event',
    serviceFormType: ServiceFormType.PORTER_SERVICE,
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 5,
    name: 'Mandatory Storage Service',
    description: 'Required storage service for all events',
    serviceFormType: ServiceFormType.MANDATORY_STORAGE_SERVICE,
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 6,
    name: 'Forklift (for Installation and Removal)',
    description: 'Forklift services for heavy equipment handling',
    serviceFormType: ServiceFormType.FORKLIFT_SERVICE,
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
];

// Mock storage for show services
let mockShowServices: ShowService[] = [];

// Helper function to simulate API delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

const ServiceQuery = {
  tags: ['Service'] as const,

  // Get all available services
  getAll: async (): Promise<Service[]> => {
    await delay(500); // Simulate API delay
    return mockServices;
  },

  // Get service by ID
  getById: async (id: number): Promise<Service> => {
    await delay(300);
    const service = mockServices.find((s) => s.id === id);
    if (!service) {
      throw new Error(`Service with id ${id} not found`);
    }
    return service;
  },

  // Get services for a specific show
  getShowServices: async (showId: number): Promise<ShowService[]> => {
    await delay(400);
    return mockShowServices.filter((ss) => ss.showId === showId);
  },

  // Create or update show service selection
  createShowService: async (
    data: CreateShowServiceRequest,
  ): Promise<boolean> => {
    await delay(600);

    // Check if service already exists for this show
    const existingIndex = mockShowServices.findIndex(
      (ss) => ss.showId === data.showId && ss.serviceId === data.serviceId,
    );

    if (existingIndex >= 0) {
      // Update existing
      mockShowServices[existingIndex] = {
        ...mockShowServices[existingIndex],
        isSelected: data.isSelected,
        formData: data.formData,
        updatedAt: new Date().toISOString(),
      };
    } else {
      // Create new
      const service = mockServices.find((s) => s.id === data.serviceId);
      if (service) {
        mockShowServices.push({
          id: mockShowServices.length + 1,
          showId: data.showId,
          serviceId: data.serviceId,
          isSelected: data.isSelected,
          formData: data.formData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          service,
        });
      }
    }

    return true;
  },

  // Update show service selection
  updateShowService:
    (showId: number, serviceId: number) =>
    async (data: UpdateShowServiceRequest): Promise<boolean> => {
      await delay(500);

      const existingIndex = mockShowServices.findIndex(
        (ss) => ss.showId === showId && ss.serviceId === serviceId,
      );

      if (existingIndex >= 0) {
        mockShowServices[existingIndex] = {
          ...mockShowServices[existingIndex],
          isSelected: data.isSelected,
          formData: data.formData,
          updatedAt: new Date().toISOString(),
        };
        return true;
      }

      throw new Error('Show service not found');
    },

  // Delete show service selection
  deleteShowService: async (
    showId: number,
    serviceId: number,
  ): Promise<boolean> => {
    await delay(400);

    const initialLength = mockShowServices.length;
    mockShowServices = mockShowServices.filter(
      (ss) => !(ss.showId === showId && ss.serviceId === serviceId),
    );

    return mockShowServices.length < initialLength;
  },

  // Bulk update show services
  bulkUpdateShowServices: async (
    showId: number,
    services: CreateShowServiceRequest[],
  ): Promise<boolean> => {
    await delay(800);

    // Remove existing services for this show
    mockShowServices = mockShowServices.filter((ss) => ss.showId !== showId);

    // Add new services
    for (const serviceData of services) {
      const service = mockServices.find((s) => s.id === serviceData.serviceId);
      if (service) {
        mockShowServices.push({
          id: mockShowServices.length + 1,
          showId: serviceData.showId,
          serviceId: serviceData.serviceId,
          isSelected: serviceData.isSelected,
          formData: serviceData.formData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          service,
        });
      }
    }

    return true;
  },
};

export default ServiceQuery;
