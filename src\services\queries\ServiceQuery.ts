import {
  Service,
  ShowService,
  CreateShowServiceRequest,
  UpdateShowServiceRequest,
} from '@/models/Service';
import fetcher from './fetcher';

const ServiceQuery = {
  tags: ['Service'] as const,

  // Get all available services
  getAll: async () => fetcher<Service[]>('Services'),

  // Get service by ID
  getById: async (id: number) => fetcher<Service>(`Services/${id}`),

  // Get services for a specific show
  getShowServices: async (showId: number) =>
    fetcher<ShowService[]>(`Shows/${showId}/services`),

  // Create or update show service selection
  createShowService: async (data: CreateShowServiceRequest) =>
    fetcher<boolean>('Shows/services', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  // Update show service selection
  updateShowService: (showId: number, serviceId: number) => 
    async (data: UpdateShowServiceRequest) =>
      fetcher<boolean>(`Shows/${showId}/services/${serviceId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      }),

  // Delete show service selection
  deleteShowService: async (showId: number, serviceId: number) =>
    fetcher<boolean>(`Shows/${showId}/services/${serviceId}`, {
      method: 'DELETE',
    }),

  // Bulk update show services
  bulkUpdateShowServices: async (showId: number, services: CreateShowServiceRequest[]) =>
    fetcher<boolean>(`Shows/${showId}/services/bulk`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(services),
    }),
};

export default ServiceQuery;
