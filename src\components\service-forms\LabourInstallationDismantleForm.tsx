import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  LabourInstallationDismantleSchema,
  LabourInstallationDismantleFormType,
} from '@/schema/ServiceFormSchemas';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';

interface LabourInstallationDismantleFormProps {
  onSubmit: (data: LabourInstallationDismantleFormType) => void;
  initialData?: Partial<LabourInstallationDismantleFormType>;
  isLoading?: boolean;
}

export function LabourInstallationDismantleForm({
  onSubmit,
  initialData,
  isLoading = false,
}: LabourInstallationDismantleFormProps) {
  const form = useForm<LabourInstallationDismantleFormType>({
    resolver: zodResolver(LabourInstallationDismantleSchema),
    defaultValues: {
      serviceIncludes: initialData?.serviceIncludes || '',
      minimumHour: initialData?.minimumHour || '2_hours',
      regularTimeRate: initialData?.regularTimeRate || 0,
      minimumCharge: initialData?.minimumCharge || 0,
      daysHours: initialData?.daysHours || '',
      overtimeRate: initialData?.overtimeRate || 0,
      overtimeMinimumCharge: initialData?.overtimeMinimumCharge || 0,
      overtimeDaysHours: initialData?.overtimeDaysHours || '',
      doubleTimeRate: initialData?.doubleTimeRate || 0,
      doubleTimeMinimumCharge: initialData?.doubleTimeMinimumCharge || 0,
      doubleTimeDaysHours: initialData?.doubleTimeDaysHours || '',
      supervisionRate: initialData?.supervisionRate || 0,
      supervisorNotes: initialData?.supervisorNotes || '',
      ladderPrices: {
        sixFeet: initialData?.ladderPrices?.sixFeet || 0,
        eightFeet: initialData?.ladderPrices?.eightFeet || 0,
        tenFeet: initialData?.ladderPrices?.tenFeet || 0,
        twelveFeet: initialData?.ladderPrices?.twelveFeet || 0,
      },
    },
  });

  const handleSubmit = (data: LabourInstallationDismantleFormType) => {
    onSubmit(data);
  };

  return (
    <div className="space-y-6">
      <div className="border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          Labour (Installation and Dismantle)
        </h3>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            <FormField
              control={form.control}
              name="serviceIncludes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Service Includes:</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe what services are included..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="minimumHour"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Minimum Hour:</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex items-center space-x-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1_hour" id="1_hour" />
                        <Label htmlFor="1_hour">1 hour</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2_hours" id="2_hours" />
                        <Label htmlFor="2_hours">2 hours</Label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Regular Time Section */}
            <div className="space-y-4">
              <h4 className="font-medium">Regular Time:</h4>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="regularTimeRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Rate:</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            {...field}
                            onChange={(e) =>
                              field.onChange(parseFloat(e.target.value) || 0)
                            }
                          />
                          <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                            $ / hr
                          </span>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="minimumCharge"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Minimum charge:</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            {...field}
                            onChange={(e) =>
                              field.onChange(parseFloat(e.target.value) || 0)
                            }
                          />
                          <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                            $
                          </span>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="daysHours"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Days & Hours:</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Specify days and hours..."
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Overtime Section */}
            <div className="space-y-4">
              <h4 className="font-medium">Overtime:</h4>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="overtimeRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Rate:</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            {...field}
                            onChange={(e) =>
                              field.onChange(parseFloat(e.target.value) || 0)
                            }
                          />
                          <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                            $ / hr
                          </span>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="overtimeMinimumCharge"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Minimum charge:</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            {...field}
                            onChange={(e) =>
                              field.onChange(parseFloat(e.target.value) || 0)
                            }
                          />
                          <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                            $
                          </span>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="overtimeDaysHours"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Days & Hours:</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Specify days and hours..."
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Double Time Section */}
            <div className="space-y-4">
              <h4 className="font-medium">Double Time:</h4>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="doubleTimeRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Rate:</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            {...field}
                            onChange={(e) =>
                              field.onChange(parseFloat(e.target.value) || 0)
                            }
                          />
                          <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                            $ / hr
                          </span>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="doubleTimeMinimumCharge"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Minimum charge:</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            {...field}
                            onChange={(e) =>
                              field.onChange(parseFloat(e.target.value) || 0)
                            }
                          />
                          <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                            $
                          </span>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="doubleTimeDaysHours"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Days & Hours:</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Specify days and hours..."
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Supervision Rate */}
            <FormField
              control={form.control}
              name="supervisionRate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Supervision Rate:</FormLabel>
                  <FormControl>
                    <div className="relative max-w-xs">
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        %
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Supervisor's Notes */}
            <FormField
              control={form.control}
              name="supervisorNotes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Supervisor's Notes:</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Additional notes..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Ladder Prices */}
            <div className="space-y-4">
              <FormLabel>Ladder Price:</FormLabel>
              <div className="grid grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm">$ 27.00 - 6' Height</Label>
                  <FormField
                    control={form.control}
                    name="ladderPrices.sixFeet"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            {...field}
                            onChange={(e) =>
                              field.onChange(parseFloat(e.target.value) || 0)
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-sm">$ 30.00 - 8' Height</Label>
                  <FormField
                    control={form.control}
                    name="ladderPrices.eightFeet"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            {...field}
                            onChange={(e) =>
                              field.onChange(parseFloat(e.target.value) || 0)
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-sm">$ 35.00 - 10' Height</Label>
                  <FormField
                    control={form.control}
                    name="ladderPrices.tenFeet"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            {...field}
                            onChange={(e) =>
                              field.onChange(parseFloat(e.target.value) || 0)
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-sm">$ 40.00 - 12' Height</Label>
                  <FormField
                    control={form.control}
                    name="ladderPrices.twelveFeet"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            {...field}
                            onChange={(e) =>
                              field.onChange(parseFloat(e.target.value) || 0)
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save Information'}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
