import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  LabourInstallationDismantleSchema,
  LabourInstallationDismantleFormType,
} from '@/schema/ServiceFormSchemas';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';

interface LabourInstallationDismantleFormProps {
  onSubmit: (data: LabourInstallationDismantleFormType) => void;
  initialData?: Partial<LabourInstallationDismantleFormType>;
  isLoading?: boolean;
}

export function LabourInstallationDismantleForm({
  onSubmit,
  initialData,
  isLoading = false,
}: LabourInstallationDismantleFormProps) {
  const form = useForm<LabourInstallationDismantleFormType>({
    resolver: zodResolver(LabourInstallationDismantleSchema),
    defaultValues: {
      serviceIncludes: initialData?.serviceIncludes || '',
      minimumHour: initialData?.minimumHour || '2_hours',
      regularTimeRate: initialData?.regularTimeRate || 0,
      minimumCharge: initialData?.minimumCharge || 0,
      daysHours: initialData?.daysHours || '',
      overtimeRate: initialData?.overtimeRate || 0,
      overtimeMinimumCharge: initialData?.overtimeMinimumCharge || 0,
      overtimeDaysHours: initialData?.overtimeDaysHours || '',
      doubleTimeRate: initialData?.doubleTimeRate || 0,
      doubleTimeMinimumCharge: initialData?.doubleTimeMinimumCharge || 0,
      doubleTimeDaysHours: initialData?.doubleTimeDaysHours || '',
      supervisionRate: initialData?.supervisionRate || 0,
      supervisorNotes: initialData?.supervisorNotes || '',
      ladderPrices: {
        sixFeet: initialData?.ladderPrices?.sixFeet || 0,
        eightFeet: initialData?.ladderPrices?.eightFeet || 0,
        tenFeet: initialData?.ladderPrices?.tenFeet || 0,
        twelveFeet: initialData?.ladderPrices?.twelveFeet || 0,
      },
    },
  });

  const handleSubmit = (data: LabourInstallationDismantleFormType) => {
    onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <Field
          control={form.control}
          name="serviceIncludes"
          type="textarea"
          label="Service Includes"
          placeholder="Describe what services are included..."
          className="min-h-[100px]"
        />

        <Field
          control={form.control}
          name="minimumHour"
          type={{
            type: 'RadioGroup',
            props: {
              options: [
                { label: '1 hour', value: '1_hour' },
                { label: '2 hours', value: '2_hours' },
              ],
            },
          }}
          label="Minimum Hour"
          containerClassName="flex items-center space-x-6"
        />

        {/* Regular Time Section */}
        <div className="space-y-4">
          <h4 className="font-medium">Regular Time:</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="relative">
              <Field
                control={form.control}
                name="regularTimeRate"
                type="number"
                label="Rate"
                step="0.01"
                min="0"
              />
              <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                $ / hr
              </span>
            </div>

            <div className="relative">
              <Field
                control={form.control}
                name="minimumCharge"
                type="number"
                label="Minimum charge"
                step="0.01"
                min="0"
              />
              <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                $
              </span>
            </div>
          </div>

          <Field
            control={form.control}
            name="daysHours"
            type="textarea"
            label="Days & Hours"
            placeholder="Specify days and hours..."
            className="min-h-[80px]"
          />
        </div>

        {/* Overtime Section */}
        <div className="space-y-4">
          <h4 className="font-medium">Overtime:</h4>
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="overtimeRate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Rate:</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        $ / hr
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="overtimeMinimumCharge"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Minimum charge:</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        $
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="overtimeDaysHours"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Days & Hours:</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Specify days and hours..."
                    className="min-h-[80px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Double Time Section */}
        <div className="space-y-4">
          <h4 className="font-medium">Double Time:</h4>
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="doubleTimeRate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Rate:</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        $ / hr
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="doubleTimeMinimumCharge"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Minimum charge:</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                        $
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="doubleTimeDaysHours"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Days & Hours:</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Specify days and hours..."
                    className="min-h-[80px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Supervision Rate */}
        <FormField
          control={form.control}
          name="supervisionRate"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Supervision Rate:</FormLabel>
              <FormControl>
                <div className="relative max-w-xs">
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    {...field}
                    onChange={(e) =>
                      field.onChange(parseFloat(e.target.value) || 0)
                    }
                  />
                  <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                    %
                  </span>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Supervisor's Notes */}
        <FormField
          control={form.control}
          name="supervisorNotes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Supervisor's Notes:</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Additional notes..."
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Ladder Prices */}
        <div className="space-y-4">
          <FormLabel>Ladder Price:</FormLabel>
          <div className="grid grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label className="text-sm">$ 27.00 - 6' Height</Label>
              <FormField
                control={form.control}
                name="ladderPrices.sixFeet"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm">$ 30.00 - 8' Height</Label>
              <FormField
                control={form.control}
                name="ladderPrices.eightFeet"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm">$ 35.00 - 10' Height</Label>
              <FormField
                control={form.control}
                name="ladderPrices.tenFeet"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm">$ 40.00 - 12' Height</Label>
              <FormField
                control={form.control}
                name="ladderPrices.twelveFeet"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Saving...' : 'Save Information'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
