import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { ReactNode } from 'react';
import { ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { filterFieldType } from '@/components/ui/data-table';
import Checkbox from '@/components/ui/inputs/checkbox';
import { OptionType } from '@/components/ui/inputs/field/field';
import { cn } from '@/lib/utils';

import { formatHeaderString } from './utils';

interface Data {
  [key: string]: any;
}

// Sort icon component that shows the appropriate icon based on sort state
const SortIcon = ({
  sortDirection,
}: {
  sortDirection: false | 'asc' | 'desc';
}) => {
  if (sortDirection === 'asc') {
    return <ArrowUp className="ml-2 h-4 w-4 text-blue-600" />;
  }
  if (sortDirection === 'desc') {
    return <ArrowDown className="ml-2 h-4 w-4 text-blue-600" />;
  }
  return <ArrowUpDown className="ml-2 h-4 w-4 text-gray-400" />;
};
type AttributeType<T, Key extends keyof T, TData> =
  | 'text'
  | { type: 'date'; format?: string }
  | {
      type: 'node';
      render: (props: { row: TData; cell: T[Key] }) => ReactNode;
    };

type Attribute<T> = {
  [Key in keyof T]?: {
    name: string;
    type: AttributeType<T, Key, T>;
    sortable?: boolean;
    orderable?: boolean;
  };
};
type UnmappedAttribute<T, TData> = {
  [Key in keyof T]: {
    name: string;
    type: AttributeType<T, Key, TData>;
    sortable?: boolean;

    value?: T[Key];
  };
};
export const generateColumns = <TData extends Data>(
  data: TData[],
  additionalColumns: ColumnDef<TData>[] = [],
  withSelect: boolean = true,
): ColumnDef<TData>[] => {
  const selectCol: ColumnDef<TData> = {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  };
  const columns =
    data.length > 0
      ? Object.keys(data[0]).map((key): ColumnDef<TData> => {
          return {
            accessorKey: key,
            header: ({ column }) => {
              return (
                <Button
                  variant={'default'}
                  onClick={() =>
                    column.toggleSorting(column.getIsSorted() === 'asc')
                  }
                  className={cn(
                    'justify-start px-0 hover:bg-gray-100',
                    column.getIsSorted() && 'text-blue-600',
                  )}
                >
                  {formatHeaderString(key).toUpperCase()}
                  <SortIcon sortDirection={column.getIsSorted()} />
                </Button>
              );
            },

            cell: ({ row }) => {
              const cellData = row.original[key];
              if (cellData == null || cellData == undefined) return '-';
              return cellData instanceof Date
                ? format(cellData, 'MM/dd/yyyy')
                : cellData?.toString();
            },
          };
        })
      : [];
  return [withSelect && selectCol, ...columns, ...additionalColumns].filter(
    Boolean,
  ) as ColumnDef<TData>[];
};

export const generateTableColumns = <TData extends Data & { id: number }>(
  attributes: Attribute<TData>,
  unmapped?: UnmappedAttribute<{ [key: string]: any }, TData>,
  withSelect: boolean = false,
  withDrag: boolean = false,
  defaultSort?: { column: keyof TData; direction: 'asc' | 'desc' },
): ColumnDef<TData>[] => {
  const selectCol: ColumnDef<TData> = {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  };

  const dragCol: ColumnDef<TData> = {
    id: 'drag',
    header: () => <span className="text-foreground  ">Move</span>,
    enableSorting: false,
    enableHiding: false,
  };
  const columns = Object.entries(attributes).map(
    ([key, attribute]): ColumnDef<TData> => {
      return {
        accessorKey: key,
        header: ({ column }) => {
          return (
            <Button
              variant="link"
              // nonFunctional={!attribute.sortable}
              onClick={() =>
                column.toggleSorting(column.getIsSorted() === 'asc')
              }
              className={cn(
                'justify-start px-0 hover:bg-gray-100',
                column.getIsSorted() && 'text-blue-600',
              )}
            >
              {attribute.name}
              {attribute.sortable && (
                <SortIcon sortDirection={column.getIsSorted()} />
              )}
            </Button>
          );
        },
        cell: ({ row }) => {
          const cellData = row.original[key];
          if (cellData == null || cellData == undefined) return '-';
          if (typeof attribute.type === 'string') {
            if (attribute.type == 'text') return cellData;
            else
              return cellData instanceof Date
                ? format(cellData, 'MM/dd/yyyy')
                : cellData?.toString();
          }
          if ('type' in attribute.type) {
            if (attribute.type.type === 'node')
              return attribute.type.render({
                row: row.original,
                cell: cellData,
              });
            if (attribute.type.type === 'date')
              return format(cellData, attribute.type.format || 'MM/dd/yyyy');
          }
        },
        filterFn: (row, columnId, filterValue) => {
          const cellData = row.original[columnId as keyof TData];
          const att = attributes[columnId as keyof TData];

          if (!filterValue || !att) return true;

          if (att.type === 'text') {
            const lowerCaseCellData = cellData?.toString().toLowerCase();
            const lowerCaseFilterValue =
              typeof filterValue === 'string' ? filterValue.toLowerCase() : '';

            if (Array.isArray(filterValue)) {
              return filterValue.some((filterItem) =>
                lowerCaseCellData?.includes(filterItem.toLowerCase()),
              );
            }
            return lowerCaseCellData?.includes(lowerCaseFilterValue);
          }

          if (typeof att.type !== 'string' && 'type' in att.type) {
            if (att.type.type === 'node') {
              if (Array.isArray(filterValue) && filterValue.length > 0) {
                if (typeof cellData === 'boolean') {
                  return filterValue.includes(String(cellData));
                }
                if (typeof cellData === 'object' && cellData?.name) {
                  return filterValue.some(
                    (filterItem) =>
                      cellData.name.toLowerCase() === filterItem.toLowerCase(),
                  );
                }
              }
              return true;
            }
            if (att.type.type === 'date') {
              const from = filterValue.from ? new Date(filterValue.from) : null;
              const to = filterValue.to ? new Date(filterValue.to) : null;
              const cellDataDate = new Date(cellData);

              if (!from && !to) return true;
              const fromValid = !from || cellDataDate >= from;
              const toValid = !to || cellDataDate <= to;
              return fromValid && toValid;
            }
          }
          return true;
        },
      };
    },
  );

  const additionalColumns = unmapped
    ? Object.entries(unmapped).map(([key, attribute]): ColumnDef<TData> => {
        return {
          accessorKey: key,
          header: ({ column }) => {
            return (
              <Button
                variant="link"
                // nonFunctional={!attribute.sortable}
                onClick={() =>
                  column.toggleSorting(column.getIsSorted() === 'asc')
                }
                className={cn(
                  'justify-start px-0 hover:bg-gray-100',
                  column.getIsSorted() && 'text-blue-600',
                )}
              >
                {attribute.name}
                {attribute.sortable && (
                  <SortIcon sortDirection={column.getIsSorted()} />
                )}
              </Button>
            );
          },

          cell: ({ row }) => {
            const cellData = attribute.value ?? '-';
            if (typeof attribute.type === 'string') {
              if (attribute.type == 'text') return cellData;
              else
                return cellData instanceof Date
                  ? format(cellData, 'MM/dd/yyyy')
                  : cellData?.toString();
            }
            if ('type' in attribute.type) {
              if (attribute.type.type === 'node')
                return attribute.type.render({
                  row: row.original,
                  cell: cellData,
                });
              if (attribute.type.type === 'date')
                return format(cellData, attribute.type.format ?? 'MM/dd/yyyy');
            }
          },
        };
      })
    : [];
  return [
    withDrag && dragCol,
    withSelect && selectCol,
    ...columns,
    ...additionalColumns,
  ].filter(Boolean) as ColumnDef<TData>[];
};

type FilterAttributeType<T, Key extends keyof T, TData> =
  | 'text'
  | 'date'
  | {
      type: 'select';
      options: OptionType[];
    };

type FilterAttribute<T> = {
  [Key in keyof T]?: {
    name: string;
    type: FilterAttributeType<T, Key, T>;
  };
};

export const generateTableFilters = <TData extends Data>(
  filterAttributes: FilterAttribute<TData>,
): filterFieldType<TData>[] => {
  const filters = Object.entries(filterAttributes)
    .filter(([_, att]) => att)
    .map(([key, attribute]): filterFieldType<TData> => {
      return {
        id: key,
        name: attribute!.name,
        type: attribute!.type,
      };
    });
  return filters;
};
