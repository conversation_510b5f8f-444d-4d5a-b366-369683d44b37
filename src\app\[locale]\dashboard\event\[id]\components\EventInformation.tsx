import { Calendar, MapPin } from 'lucide-react';

function formatDateRange(startDate: string, endDate: string) {
  const start = new Date(startDate);
  const end = new Date(endDate);

  const startMonth = start.toLocaleDateString('en-US', { month: 'long' });
  const endMonth = end.toLocaleDateString('en-US', { month: 'long' });

  if (startMonth === endMonth) {
    return `${startMonth} ${start.getDate()} - ${end.getDate()}, ${end.getFullYear()}`;
  } else {
    return `${startMonth} ${start.getDate()} - ${endMonth} ${end.getDate()}, ${end.getFullYear()}`;
  }
}

export default function EventInformation({ show, locationName }: { show: any, locationName?: string }) {
  if (!show) {
    return null;
  }

  return (
    <div className="mb-6 rounded-lg overflow-hidden shadow-md">
      <div className="bg-gradient-to-r from-[#00646C] to-[#00646C]/70 text-white p-4">
        <h2 className="text-xl font-semibold">Event Information</h2>
      </div>
      <div className="bg-white p-6 border border-t-0 border-slate-200 rounded-b-lg">
        <div className="flex flex-wrap md:flex-nowrap items-center gap-8">
          <div className="w-full md:w-1/3">
            <h3 className="font-medium text-slate-800 mb-2">Event Name</h3>
            <p className="text-lg">{show.name}</p>
          </div>
          <div className="w-full md:w-1/3">
            <h3 className="font-medium text-slate-800 mb-2">Location</h3>
            <div className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-[#00646C]" />
              <span className="text-lg">{locationName}</span>
            </div>
          </div>
          <div className="w-full md:w-1/3">
            <h3 className="font-medium text-slate-800 mb-2">Event Dates</h3>
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-[#00646C]" />
              <span className="text-lg">
                {formatDateRange(show.startDate, show.endDate)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}