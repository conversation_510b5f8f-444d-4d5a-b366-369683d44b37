'use client';

import { useQuery } from '@tanstack/react-query';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import WarehouseQuery from '@/services/queries/WarehouseQuery';
import { ProvinceQuery } from '@/services/queries/ProvinceQuery';
import { WarehouseSummaryDto } from '@/models/Warehouse';
import { Button } from '@/components/ui/button';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import { CheckCircle, Edit2, XCircle } from 'lucide-react';
import { FaPlus } from 'react-icons/fa';
import InventoryModal from '../inventory_modal';

export const InventoryTable = () => {
  // Fetch warehouses with warehouseType = 1 (Advance Receiving)
  const { data, isLoading } = useQuery({
    queryKey: [WarehouseQuery.tags, { warehouseType: 2 }],
    queryFn: () => WarehouseQuery.getAll(2),
  });

  // Fetch provinces for filter dropdown
  const { data: provinces, isLoading: isLoadingProvinces } = useQuery({
    queryKey: ProvinceQuery.tags,
    queryFn: ProvinceQuery.getAll,
  });

  const filters = generateTableFilters({
    warehouseName: {
      name: 'Name',
      type: 'text',
    },
    isActive: {
      name: 'Active',
      type: {
        type: 'select',
        options: [
          { label: 'Active', value: 'true' },
          { label: 'Inactive', value: 'false' },
        ],
      },
    },
    provinceName: {
      name: 'Province',
      type: {
        type: 'select',
        options:
          provinces?.map((province) => ({
            label: province.name,
            value: province.name,
          })) || [],
      },
    },
  });

  const columns = generateTableColumns<WarehouseSummaryDto>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      code: { name: 'Code', type: 'text', sortable: true },
      warehouseName: { name: 'Warehouse Name', type: 'text', sortable: true },
      phone: { name: 'Phone', type: 'text', sortable: true },
      city: { name: 'City', type: 'text', sortable: true },
      provinceName: { name: 'Province', type: 'text', sortable: true },
      isActive: {
        name: 'Active',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <>
                  <CheckCircle className="text-green-600 w-4 h-4 mr-1" />
                  <span className="text-green-600 hidden">Active</span>
                </>
              ) : (
                <>
                  <XCircle className="text-red-600 w-4 h-4 mr-1" />
                  <span className="text-red-600 hidden">Inactive</span>
                </>
              )}
            </div>
          ),
        },
        sortable: true,
      },
      // contactPersonName: {
      //   name: 'Contact Person',
      //   type: 'text',
      //   sortable: false,
      // },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <Button
              size="sm"
              variant="secondary"
              iconName="EditIcon"
              onClick={() => {
                modal(<InventoryModal id={row.id} />, {
                  ...DEFAULT_MODAL,
                  width: '30%',
                }).open();
              }}
            ></Button>
          ),
        },
      },
    },
    false,
  );

  return (
    <DataTable
      data={data}
      columns={columns}
      filterFields={filters}
      isLoading={isLoading || isLoadingProvinces}
      controls={
        <Button
          variant="primary"
          iconName="AddIcon"
          onClick={() => {
            modal(<InventoryModal />, {
              ...DEFAULT_MODAL,
              width: '30%',
            }).open();
          }}
        >
          Add New Warehouse
        </Button>
      }
    />
  );
};

export default InventoryTable;
