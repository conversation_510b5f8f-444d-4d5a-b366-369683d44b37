'use client';

import { useQuery } from '@tanstack/react-query';
import Link from 'next/link';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns } from '@/lib/tableUtils';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { Button } from '@/components/ui/button';
import { CalendarIcon, MapPinIcon } from '@/assets/Icons';
import { CompanyShow } from '@/models/Company';

interface ShowsTableProps {
  companyId: number;
  companyName: string;
}

export const ShowsTable = ({ companyId, companyName }: ShowsTableProps) => {
  const { data, error, isLoading } = useQuery<CompanyShow[]>({
    queryKey: [...CompanyQuery.tags, 'shows', companyId],
    queryFn: () => CompanyQuery.getShows(companyId),
  });

  const columns = generateTableColumns<CompanyShow>(
    {
      id: { name: 'ID', type: 'text' },
      name: { name: 'Show Name', type: 'text' },
      code: { name: 'Show Code', type: 'text' },
      startDate: {
        name: 'Start Date',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center gap-2">
              <CalendarIcon size={14} className="text-gray-500" />
              <span>{new Date(cell).toLocaleDateString()}</span>
            </div>
          ),
        },
      },
      endDate: {
        name: 'End Date',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center gap-2">
              <CalendarIcon size={14} className="text-gray-500" />
              <span>{new Date(cell).toLocaleDateString()}</span>
            </div>
          ),
        },
      },
      venueName: {
        name: 'Venue',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center gap-2">
              <MapPinIcon size={14} className="text-gray-500" />
              <span>{cell}</span>
            </div>
          ),
        },
      },
      city: { name: 'City', type: 'text' },
      province: { name: 'Province', type: 'text' },
      archive: {
        name: 'Status',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <span className="text-xs bg-gray-200 text-gray-700 px-1.5 py-0.5 rounded-full">
                  Archived
                </span>
              ) : (
                <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded-full">
                  Active
                </span>
              )}
            </div>
          ),
        },
      },
    },
    {
      actions: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex items-center gap-2">
              <Link href={`/dashboard/setup/list-of-shows/${row.id}`}>
                <Button variant="outline" size="sm" iconName="EyeIcon"></Button>
              </Link>
            </div>
          ),
        },
      },
    },
  );

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Failed to load shows for {companyName}</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-32 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="flex flex-col items-center gap-4">
          <CalendarIcon size={48} className="text-gray-300" />
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No Shows Found
            </h3>
            <p className="text-gray-500">
              {companyName} is not currently managing any shows.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return <DataTable data={data} columns={columns} />;
};
