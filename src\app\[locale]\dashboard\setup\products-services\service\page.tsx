import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import ServiceTableAccordion from './components/service_table_accordion';

export const metadata: Metadata = {
  title: 'Goodkey | Service',
};

export default async function ShowFacilityPage() {
  const queryClient = getQueryClient();
  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        {
          title: 'Products & Services',
          link: '/dashboard/setup',
        },
        {
          title: 'Service',
          link: '/dashboard/setup/products-services/service',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <ServiceTableAccordion />
      </HydrationBoundary>
    </AppLayout>
  );
}
