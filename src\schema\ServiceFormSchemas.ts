import { z } from 'zod';

// Ground Handling Service Schema
export const GroundHandlingSchema = z.object({
  equipmentRequired: z.array(z.string()).min(1, 'At least one equipment is required'),
  numberOfWorkers: z.number().min(1, 'At least 1 worker is required'),
  workingHours: z.object({
    startTime: z.string().min(1, 'Start time is required'),
    endTime: z.string().min(1, 'End time is required'),
  }),
  specialRequirements: z.string().optional(),
  contactPerson: z.string().min(1, 'Contact person is required'),
  contactPhone: z.string().min(1, 'Contact phone is required'),
  estimatedCost: z.number().min(0, 'Cost must be positive'),
});

// Electrical Service Schema
export const ElectricalSchema = z.object({
  powerRequirement: z.number().min(1, 'Power requirement is required'),
  numberOfOutlets: z.number().min(1, 'At least 1 outlet is required'),
  voltageType: z.enum(['110V', '220V', '440V']),
  specialEquipment: z.array(z.string()).optional(),
  installationDate: z.string().min(1, 'Installation date is required'),
  removalDate: z.string().min(1, 'Removal date is required'),
  electricianRequired: z.boolean(),
  safetyRequirements: z.string().optional(),
  estimatedCost: z.number().min(0, 'Cost must be positive'),
});

// Plumbing Service Schema
export const PlumbingSchema = z.object({
  waterSupplyRequired: z.boolean(),
  drainageRequired: z.boolean(),
  numberOfFixtures: z.number().min(0, 'Number of fixtures must be positive'),
  fixtureTypes: z.array(z.string()).optional(),
  installationDate: z.string().min(1, 'Installation date is required'),
  removalDate: z.string().min(1, 'Removal date is required'),
  plumberRequired: z.boolean(),
  specialRequirements: z.string().optional(),
  estimatedCost: z.number().min(0, 'Cost must be positive'),
});

// Cleaning Service Schema
export const CleaningSchema = z.object({
  cleaningType: z.enum(['DAILY', 'WEEKLY', 'EVENT_BASED', 'DEEP_CLEANING']),
  areasToClean: z.array(z.string()).min(1, 'At least one area is required'),
  frequency: z.string().min(1, 'Frequency is required'),
  specialRequirements: z.string().optional(),
  cleaningSupplies: z.array(z.string()).optional(),
  numberOfCleaners: z.number().min(1, 'At least 1 cleaner is required'),
  workingHours: z.object({
    startTime: z.string().min(1, 'Start time is required'),
    endTime: z.string().min(1, 'End time is required'),
  }),
  estimatedCost: z.number().min(0, 'Cost must be positive'),
});

// Security Service Schema
export const SecuritySchema = z.object({
  securityType: z.enum(['GUARDS', 'CAMERAS', 'ACCESS_CONTROL', 'ALARM_SYSTEM']),
  numberOfGuards: z.number().min(0, 'Number of guards must be positive'),
  coverageAreas: z.array(z.string()).min(1, 'At least one coverage area is required'),
  workingHours: z.object({
    startTime: z.string().min(1, 'Start time is required'),
    endTime: z.string().min(1, 'End time is required'),
  }),
  specialRequirements: z.string().optional(),
  equipmentNeeded: z.array(z.string()).optional(),
  emergencyContact: z.string().min(1, 'Emergency contact is required'),
  estimatedCost: z.number().min(0, 'Cost must be positive'),
});

// Catering Service Schema
export const CateringSchema = z.object({
  mealType: z.enum(['BREAKFAST', 'LUNCH', 'DINNER', 'SNACKS', 'BEVERAGES']),
  numberOfPeople: z.number().min(1, 'At least 1 person is required'),
  dietaryRestrictions: z.array(z.string()).optional(),
  menuPreferences: z.string().optional(),
  servingDate: z.string().min(1, 'Serving date is required'),
  servingTime: z.string().min(1, 'Serving time is required'),
  setupRequirements: z.string().optional(),
  specialRequirements: z.string().optional(),
  estimatedCost: z.number().min(0, 'Cost must be positive'),
});

// Audio Visual Service Schema
export const AudioVisualSchema = z.object({
  equipmentType: z.array(z.string()).min(1, 'At least one equipment type is required'),
  soundSystemRequired: z.boolean(),
  lightingRequired: z.boolean(),
  projectionRequired: z.boolean(),
  recordingRequired: z.boolean(),
  setupDate: z.string().min(1, 'Setup date is required'),
  eventDate: z.string().min(1, 'Event date is required'),
  removalDate: z.string().min(1, 'Removal date is required'),
  technicianRequired: z.boolean(),
  specialRequirements: z.string().optional(),
  estimatedCost: z.number().min(0, 'Cost must be positive'),
});

// Decoration Service Schema
export const DecorationSchema = z.object({
  decorationType: z.array(z.string()).min(1, 'At least one decoration type is required'),
  theme: z.string().min(1, 'Theme is required'),
  colorScheme: z.array(z.string()).min(1, 'At least one color is required'),
  setupDate: z.string().min(1, 'Setup date is required'),
  eventDate: z.string().min(1, 'Event date is required'),
  removalDate: z.string().min(1, 'Removal date is required'),
  specialRequirements: z.string().optional(),
  materialsProvided: z.boolean(),
  designerRequired: z.boolean(),
  estimatedCost: z.number().min(0, 'Cost must be positive'),
});

// Transportation Service Schema
export const TransportationSchema = z.object({
  vehicleType: z.array(z.string()).min(1, 'At least one vehicle type is required'),
  numberOfVehicles: z.number().min(1, 'At least 1 vehicle is required'),
  pickupLocation: z.string().min(1, 'Pickup location is required'),
  dropoffLocation: z.string().min(1, 'Dropoff location is required'),
  pickupDate: z.string().min(1, 'Pickup date is required'),
  pickupTime: z.string().min(1, 'Pickup time is required'),
  returnDate: z.string().min(1, 'Return date is required'),
  returnTime: z.string().min(1, 'Return time is required'),
  driverRequired: z.boolean(),
  specialRequirements: z.string().optional(),
  estimatedCost: z.number().min(0, 'Cost must be positive'),
});

// Storage Service Schema
export const StorageSchema = z.object({
  storageType: z.enum(['TEMPORARY', 'LONG_TERM', 'CLIMATE_CONTROLLED']),
  storageSize: z.string().min(1, 'Storage size is required'),
  storageDuration: z.object({
    startDate: z.string().min(1, 'Start date is required'),
    endDate: z.string().min(1, 'End date is required'),
  }),
  itemsToStore: z.array(z.string()).min(1, 'At least one item is required'),
  accessRequirements: z.string().optional(),
  securityLevel: z.enum(['BASIC', 'MEDIUM', 'HIGH']),
  specialRequirements: z.string().optional(),
  estimatedCost: z.number().min(0, 'Cost must be positive'),
});

// Export all schema types
export type GroundHandlingFormType = z.infer<typeof GroundHandlingSchema>;
export type ElectricalFormType = z.infer<typeof ElectricalSchema>;
export type PlumbingFormType = z.infer<typeof PlumbingSchema>;
export type CleaningFormType = z.infer<typeof CleaningSchema>;
export type SecurityFormType = z.infer<typeof SecuritySchema>;
export type CateringFormType = z.infer<typeof CateringSchema>;
export type AudioVisualFormType = z.infer<typeof AudioVisualSchema>;
export type DecorationFormType = z.infer<typeof DecorationSchema>;
export type TransportationFormType = z.infer<typeof TransportationSchema>;
export type StorageFormType = z.infer<typeof StorageSchema>;

// Schema mapping for dynamic form rendering
export const ServiceFormSchemas = {
  GROUND_HANDLING: GroundHandlingSchema,
  ELECTRICAL: ElectricalSchema,
  PLUMBING: PlumbingSchema,
  CLEANING: CleaningSchema,
  SECURITY: SecuritySchema,
  CATERING: CateringSchema,
  AUDIO_VISUAL: AudioVisualSchema,
  DECORATION: DecorationSchema,
  TRANSPORTATION: TransportationSchema,
  STORAGE: StorageSchema,
} as const;
