# Services API Endpoints Documentation

## Overview

This document outlines the API endpoints required for the Services functionality in the GoodKey CMS system. The services system allows shows to select and configure various services with dynamic forms.

## Base URL

```
/api/v1
```

## Authentication

All endpoints require authentication via <PERSON><PERSON><PERSON> token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

---

## 📋 Services Endpoints

### 1. Get All Available Services

**GET** `/services`

Returns all available services that can be selected for shows.

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Janitorial Services",
      "description": "Professional cleaning services for your event space",
      "serviceFormType": "JANITORIAL_SERVICES",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "name": "Labour (Installation and Dismantle)",
      "description": "Professional labour for setup and breakdown",
      "serviceFormType": "LABOUR_INSTALLATION_DISMANTLE",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 2. Get Service by ID

**GET** `/services/{serviceId}`

**Parameters:**

- `serviceId` (path): Service ID

**Response:**

```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Janitorial Services",
    "description": "Professional cleaning services for your event space",
    "serviceFormType": "JANITORIAL_SERVICES",
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

---

## 🎪 Show Services Endpoints

### 3. Get Show Services

**GET** `/shows/{showId}/services`

Returns all services associated with a specific show, including selection status and form data.

**Parameters:**

- `showId` (path): Show ID

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "showId": 123,
      "serviceId": 1,
      "isSelected": true,
      "formData": {
        "serviceId": 1,
        "showId": 123,
        "formType": "JANITORIAL_SERVICES",
        "data": {
          "serviceIncludes": "Daily cleaning and maintenance",
          "rateType": "sq_ft",
          "oneTimeRate": 2.5,
          "twoDaysRate": 2.25,
          "threeDaysRate": 2.0
        }
      },
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "service": {
        "id": 1,
        "name": "Janitorial Services",
        "description": "Professional cleaning services",
        "serviceFormType": "JANITORIAL_SERVICES",
        "isActive": true
      }
    }
  ]
}
```

### 4. Create/Update Show Service Selection

**POST** `/shows/{showId}/services`

Creates or updates a service selection for a show.

**Parameters:**

- `showId` (path): Show ID

**Request Body:**

```json
{
  "serviceId": 1,
  "isSelected": true,
  "formData": {
    "serviceId": 1,
    "showId": 123,
    "formType": "JANITORIAL_SERVICES",
    "data": {
      "serviceIncludes": "Daily cleaning and maintenance",
      "rateType": "sq_ft",
      "oneTimeRate": 2.5,
      "twoDaysRate": 2.25,
      "threeDaysRate": 2.0
    }
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "Service selection updated successfully",
  "data": {
    "id": 1,
    "showId": 123,
    "serviceId": 1,
    "isSelected": true,
    "formData": {
      /* ... */
    },
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

### 5. Update Show Service Configuration

**PUT** `/shows/{showId}/services/{serviceId}`

Updates the configuration for a specific service selection.

**Parameters:**

- `showId` (path): Show ID
- `serviceId` (path): Service ID

**Request Body:**

```json
{
  "isSelected": true,
  "formData": {
    "serviceId": 1,
    "showId": 123,
    "formType": "JANITORIAL_SERVICES",
    "data": {
      "serviceIncludes": "Updated service description",
      "rateType": "hourly",
      "oneTimeRate": 25.0,
      "twoDaysRate": 22.5,
      "threeDaysRate": 20.0
    }
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "Service configuration updated successfully",
  "data": {
    "id": 1,
    "showId": 123,
    "serviceId": 1,
    "isSelected": true,
    "formData": {
      /* updated data */
    },
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

### 6. Delete Show Service Selection

**DELETE** `/shows/{showId}/services/{serviceId}`

Removes a service selection from a show.

**Parameters:**

- `showId` (path): Show ID
- `serviceId` (path): Service ID

**Response:**

```json
{
  "success": true,
  "message": "Service selection removed successfully"
}
```

### 7. Bulk Update Show Services

**PUT** `/shows/{showId}/services/bulk`

Updates multiple service selections for a show in a single request.

**Parameters:**

- `showId` (path): Show ID

**Request Body:**

```json
{
  "services": [
    {
      "serviceId": 1,
      "isSelected": true,
      "formData": {
        /* ... */
      }
    },
    {
      "serviceId": 2,
      "isSelected": false
    }
  ]
}
```

**Response:**

```json
{
  "success": true,
  "message": "Services updated successfully",
  "data": {
    "updated": 2,
    "created": 1,
    "deleted": 0
  }
}
```

---

## 📝 Service Form Data Structures

### Janitorial Services Form Data

```json
{
  "serviceIncludes": "string",
  "rateType": "sq_ft" | "hourly" | "daily",
  "oneTimeRate": "number",
  "twoDaysRate": "number",
  "threeDaysRate": "number"
}
```

### Labour Installation/Dismantle Form Data

```json
{
  "serviceIncludes": "string",
  "minimumHour": "1_hour" | "2_hours",
  "regularTimeRate": "number",
  "minimumCharge": "number",
  "daysHours": "string",
  "overtimeRate": "number",
  "overtimeMinimumCharge": "number",
  "overtimeDaysHours": "string",
  "doubleTimeRate": "number",
  "doubleTimeMinimumCharge": "number",
  "doubleTimeDaysHours": "string",
  "supervisionRate": "number",
  "supervisorNotes": "string",
  "ladderPrices": {
    "sixFeet": "number",
    "eightFeet": "number",
    "tenFeet": "number",
    "twelveFeet": "number"
  }
}
```

### Porter Service Form Data

```json
{
  "minimumHours": "number",
  "minimumDays": "number",
  "minimumLabourers": "number",
  "rateType": "sq_ft" | "hourly" | "daily",
  "rates": [
    {
      "rate": "number",
      "exhibitArea": "number"
    }
  ]
}
```

### On-Site Material Handling Form Data

```json
{
  "receivingAddress": "string",
  "daysHours": "string",
  "pricePerWeight": "number",
  "weight": "number",
  "weightUnit": "lbs" | "kg",
  "minimumCharge": "number"
}
```

### Forklift Service Form Data

```json
{
  "rates": [
    {
      "weight": "number",
      "regularRate": "number",
      "overtimeRate": "number",
      "doubleRate": "number"
    }
  ],
  "daysHours": {
    "regular": "string",
    "regularMinimumCharge": "number",
    "overtime": "string",
    "overtimeMinimumCharge": "number",
    "doubleTime": "string",
    "doubleTimeMinimumCharge": "number"
  },
  "additionalServices": {
    "manCageRate": "number",
    "boomRate": "number"
  }
}
```

### Mandatory Storage Service Form Data

```json
{
  // No additional fields - simple service selection
}
```

### Labour by Time Range Form Data

```json
{
  "shift": {
    "regularTime": {
      "from": "string",
      "to": "string",
      "weekdays": "number",
      "weekends": "number"
    },
    "overtime": {
      "from": "string",
      "to": "string",
      "weekdays": "number",
      "weekends": "number"
    },
    "doubleTime": {
      "from": "string",
      "to": "string",
      "weekdays": "number",
      "weekends": "number"
    }
  },
  "supervisionRate": "number",
  "ladderPrice": {
    "sixFeet": "number",
    "eightFeet": "number",
    "tenFeet": "number",
    "twelveFeet": "number"
  },
  "installation": {
    "minimumCharge": "number",
    "minimumHour": "number"
  },
  "dismantle": {
    "minimumCharge": "number",
    "minimumHour": "number"
  }
}
```

### Forklift by Time Range Form Data

```json
{
  "weightRanges": {
    "range1": {
      "min": "number",
      "max": "number",
      "shift": {
        "regularTime": {
          "from": "string",
          "to": "string",
          "weekdays": "number",
          "weekends": "number"
        },
        "overtime": {
          "from": "string",
          "to": "string",
          "weekdays": "number",
          "weekends": "number"
        },
        "doubleTime": {
          "from": "string",
          "to": "string",
          "weekdays": "number",
          "weekends": "number"
        }
      }
    },
    "range2": {
      /* same structure as range1 */
    },
    "range3": {
      /* same structure as range1 */
    }
  },
  "moveIn": {
    "minimumCharge": "number",
    "minimumHour": "number"
  },
  "moveOut": {
    "minimumCharge": "number",
    "minimumHour": "number"
  },
  "additionalServices": {
    "manCageRate": "number",
    "boomRate": "number"
  }
}
```

### Cart/Dolly Material Handling Form Data

```json
{
  "rentalRate": "number"
}
```

### Pre & Post Show Services Form Data

```json
{
  "serviceToShawCenter": "boolean",
  "receivingAddress": "string",
  "daysHours": "string",
  "receivingStartDate": "string",
  "cutoffDeadlineDate": "string",
  "minimumCharge": "number",
  "weightType": "lbs" | "kg",
  "cratedSkidded": {
    "price": "number",
    "perWeight": "number"
  },
  "specialHandling": {
    "price": "number",
    "perWeight": "number"
  },
  "uncratedPadWrapped": {
    "price": "number",
    "perWeight": "number"
  }
}
```

---

## 🔧 Service Form Types Enum

```typescript
enum ServiceFormType {
  JANITORIAL_SERVICES = 'JANITORIAL_SERVICES',
  LABOUR_INSTALLATION_DISMANTLE = 'LABOUR_INSTALLATION_DISMANTLE',
  LABOUR_BY_TIME_RANGE = 'LABOUR_BY_TIME_RANGE',
  ON_SITE_MATERIAL_HANDLING = 'ON_SITE_MATERIAL_HANDLING',
  PORTER_SERVICE = 'PORTER_SERVICE',
  MANDATORY_STORAGE_SERVICE = 'MANDATORY_STORAGE_SERVICE',
  FORKLIFT_SERVICE = 'FORKLIFT_SERVICE',
  FORKLIFT_BY_TIME_RANGE = 'FORKLIFT_BY_TIME_RANGE',
  CART_DOLLY_MATERIAL_HANDLING = 'CART_DOLLY_MATERIAL_HANDLING',
  PRE_POST_SHOW_SERVICES = 'PRE_POST_SHOW_SERVICES',
  ADVANCE_MATERIAL_HANDLING = 'ADVANCE_MATERIAL_HANDLING',
  LOCAL_CARTAGE = 'LOCAL_CARTAGE',
  CUSTOM_BROKERAGE = 'CUSTOM_BROKERAGE',
  GROUND_TRANSPORTATION = 'GROUND_TRANSPORTATION',
  POST_SHOW_STORAGE = 'POST_SHOW_STORAGE',
}
```

---

## ❌ Error Responses

### 400 Bad Request

```json
{
  "success": false,
  "error": "VALIDATION_ERROR",
  "message": "Invalid request data",
  "details": {
    "field": "serviceIncludes",
    "message": "Service description is required"
  }
}
```

### 404 Not Found

```json
{
  "success": false,
  "error": "NOT_FOUND",
  "message": "Service not found"
}
```

### 500 Internal Server Error

```json
{
  "success": false,
  "error": "INTERNAL_ERROR",
  "message": "An unexpected error occurred"
}
```

---

## 📊 Database Schema Requirements

### Services Table

```sql
CREATE TABLE services (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  service_form_type VARCHAR(100) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Show Services Table

```sql
CREATE TABLE show_services (
  id SERIAL PRIMARY KEY,
  show_id INTEGER NOT NULL REFERENCES shows(id),
  service_id INTEGER NOT NULL REFERENCES services(id),
  is_selected BOOLEAN DEFAULT false,
  form_data JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(show_id, service_id)
);
```

---

## 🔄 Frontend Integration Notes

1. **Service Selection**: Use checkboxes to toggle `isSelected` status
2. **Dynamic Forms**: Use `ServiceFormFactory` to render appropriate forms based on `serviceFormType`
3. **Form Data**: Store complete form data in the `formData.data` field
4. **Real-time Updates**: Use React Query for automatic cache invalidation
5. **Type Safety**: All form data structures have corresponding TypeScript interfaces

## 🚀 Implementation Status

### ✅ Implemented Services (Frontend + API Ready)

- Janitorial Services
- Labour Installation/Dismantle
- On-Site Material Handling
- Porter Service
- Mandatory Storage Service
- Forklift Service

### 🔄 Pending Implementation

- Labour by Time Range
- Forklift by Time Range
- Cart/Dolly Material Handling
- Pre & Post Show Services
- Advance Material Handling
- Local Cartage
- Custom Brokerage
- Ground Transportation
- Post Show Storage

## 📝 API Implementation Checklist

### Backend Requirements

- [ ] Create Services table with seed data
- [ ] Create Show Services table with JSONB form data storage
- [ ] Implement all 7 API endpoints
- [ ] Add validation for each service form type
- [ ] Add proper error handling and logging
- [ ] Add API rate limiting and security

### Database Considerations

- Use JSONB for flexible form data storage
- Add indexes on `show_id` and `service_id` for performance
- Consider partitioning for large datasets
- Implement soft deletes for audit trails

### Security & Validation

- Validate form data against service-specific schemas
- Ensure users can only access their own show services
- Implement proper authorization checks
- Sanitize all input data

This API design supports the current frontend implementation and provides room for future service types and features.
