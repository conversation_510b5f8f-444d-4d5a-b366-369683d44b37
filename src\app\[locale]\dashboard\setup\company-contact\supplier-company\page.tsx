import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import CompanyQuery from '@/services/queries/CompanyQuery';
import CompanyTable from './components/company_table';

export const metadata: Metadata = {
  title: 'Goodkey | Supplier Companies',
};

export default async function Cluster() {
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery({
    queryKey: [...CompanyQuery.tags, 'Supplier'],
    queryFn: () => CompanyQuery.getAll('Supplier'),
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        {
          title: 'Company Contact',
          link: '/dashboard/setup/company-contact/supplier-company',
        },
        {
          title: 'Supplier Companies',
          link: '/dashboard/setup/company-contact/supplier-company',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <CompanyTable />
      </HydrationBoundary>
    </AppLayout>
  );
}
