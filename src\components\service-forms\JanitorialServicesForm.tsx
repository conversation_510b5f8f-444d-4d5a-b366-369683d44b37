import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  JanitorialServicesSchema,
  JanitorialServicesFormType,
} from '@/schema/ServiceFormSchemas';
import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface JanitorialServicesFormProps {
  onSubmit: (data: JanitorialServicesFormType) => void;
  initialData?: Partial<JanitorialServicesFormType>;
  isLoading?: boolean;
}

export function JanitorialServicesForm({
  onSubmit,
  initialData,
  isLoading = false,
}: JanitorialServicesFormProps) {
  const form = useForm<JanitorialServicesFormType>({
    resolver: zodResolver(JanitorialServicesSchema),
    defaultValues: {
      serviceIncludes: initialData?.serviceIncludes || '',
      rateType: initialData?.rateType || 'sq_ft',
      oneTimeRate: initialData?.oneTimeRate || 0,
      twoDaysRate: initialData?.twoDaysRate || 0,
      threeDaysRate: initialData?.threeDaysRate || 0,
    },
  });

  const handleSubmit = (data: JanitorialServicesFormType) => {
    onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="serviceIncludes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Service Includes:</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Describe what services are included..."
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="rateType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Rate Type:</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select rate type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="sq_ft">sq. ft</SelectItem>
                  <SelectItem value="hourly">Hourly</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="oneTimeRate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>One Time Rate:</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseFloat(e.target.value) || 0)
                      }
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                      $
                    </span>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="twoDaysRate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Two Days Rate:</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseFloat(e.target.value) || 0)
                      }
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                      $
                    </span>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="threeDaysRate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Three Days Rate:</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseFloat(e.target.value) || 0)
                      }
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                      $
                    </span>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
          <p className="text-sm text-yellow-800">
            <strong>Note:</strong> There are currently orders on this service.
            Rate cannot be modified.
          </p>
        </div>

        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Saving...' : 'Save Information'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
