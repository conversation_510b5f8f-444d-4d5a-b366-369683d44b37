import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  JanitorialServicesSchema,
  JanitorialServicesFormType,
} from '@/schema/ServiceFormSchemas';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';

interface JanitorialServicesFormProps {
  onSubmit: (data: JanitorialServicesFormType) => void;
  initialData?: Partial<JanitorialServicesFormType>;
  isLoading?: boolean;
}

export function JanitorialServicesForm({
  onSubmit,
  initialData,
  isLoading = false,
}: JanitorialServicesFormProps) {
  const form = useForm<JanitorialServicesFormType>({
    resolver: zodResolver(JanitorialServicesSchema),
    defaultValues: {
      serviceIncludes: initialData?.serviceIncludes || '',
      rateType: initialData?.rateType || 'sq_ft',
      oneTimeRate: initialData?.oneTimeRate || 0,
      twoDaysRate: initialData?.twoDaysRate || 0,
      threeDaysRate: initialData?.threeDaysRate || 0,
    },
  });

  const handleSubmit = (data: JanitorialServicesFormType) => {
    onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <Field
          control={form.control}
          name="serviceIncludes"
          type="textarea"
          label="Service Includes"
          placeholder="Describe what services are included..."
          className="min-h-[100px]"
        />

        <Field
          control={form.control}
          name="rateType"
          type={{
            type: 'select',
            props: {
              options: [
                { label: 'sq. ft', value: 'sq_ft' },
                { label: 'Hourly', value: 'hourly' },
                { label: 'Daily', value: 'daily' },
              ],
              placeholder: 'Select rate type',
            },
          }}
          label="Rate Type"
        />

        <div className="grid grid-cols-3 gap-4">
          <div className="relative">
            <Field
              control={form.control}
              name="oneTimeRate"
              type="number"
              label="One Time Rate"
              step="0.01"
              min="0"
            />
            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
              $
            </span>
          </div>

          <div className="relative">
            <Field
              control={form.control}
              name="twoDaysRate"
              type="number"
              label="Two Days Rate"
              step="0.01"
              min="0"
            />
            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
              $
            </span>
          </div>

          <div className="relative">
            <Field
              control={form.control}
              name="threeDaysRate"
              type="number"
              label="Three Days Rate"
              step="0.01"
              min="0"
            />
            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
              $
            </span>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
          <p className="text-sm text-yellow-800">
            <strong>Note:</strong> There are currently orders on this service.
            Rate cannot be modified.
          </p>
        </div>

        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Saving...' : 'Save Information'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
