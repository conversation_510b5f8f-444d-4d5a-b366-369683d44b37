'use client';
import React, { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import PropertyQuery from '@/services/queries/PropertyQuery';
import CategoryQuery from '@/services/queries/CategoryQuery'; // import your CategoryQuery here
import { Button } from '@/components/ui/button';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import { CategoryProperty } from '@/models/Category';
import { getQueryClient } from '@/utils/query-client';
import { useRouter } from 'next/navigation';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface FormProps {
  categoryId: number;
  defaultValues?: CategoryProperty;
}

const Form: React.FC<FormProps> = ({ categoryId, defaultValues }) => {
  const { push } = useRouter();
  const { toast } = useToast();
  const [selection1, setSelection1] = useState<number | null>(
    defaultValues?.selection1 ?? null,
  );
  const [selection2, setSelection2] = useState<number | null>(
    defaultValues?.selection2 ?? null,
  );
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (defaultValues) {
      setSelection1(defaultValues.selection1 ?? null);
      setSelection2(defaultValues.selection2 ?? null);
    }
  }, [defaultValues]);

  const {
    data: properties,
    isLoading,
    isError,
  } = useQuery({
    queryKey: PropertyQuery.tags,
    queryFn: PropertyQuery.getAll,
  });

  const handleReset = () => {
    setSelection1(null);
    setSelection2(null);
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const result = await CategoryQuery.saveSelections(
        categoryId,
        selection1,
        selection2,
      );
      if (result) {
        toast({
          title: 'Success',
          description: categoryId
            ? 'Property Selection updated successfully.'
            : 'Property Selection created successfully.',
          variant: 'success',
        });
      }
    } catch (e) {
      toast({
        title: 'Error',
        description: 'Failed to save property selection.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);

      await getQueryClient().invalidateQueries({
        queryKey: ['Category', { id: Number(categoryId) }],
      });

      await getQueryClient().invalidateQueries({
        queryKey: CategoryQuery.tags,
      });

      push(`/dashboard/setup/products-services/category/${categoryId}`);
    }
  };

  if (isLoading)
    return <div className="text-[#784311] p-4">Loading properties...</div>;
  if (isError)
    return <div className="text-[#AF0061] p-4">Error fetching properties.</div>;

  return (
    <div className="overflow-x-auto px-1">
      <table className="min-w-full table-auto border border-gray-300 rounded-md overflow-hidden shadow text-sm mb-6">
        <thead className="bg-[#CDDB00] text-[#784311] uppercase text-xs tracking-wider">
          <tr>
            <th className="border px-4 py-3 text-left">Code</th>
            <th className="border px-4 py-3 text-left">Name</th>
            <th className="border px-4 py-3 text-left">Description</th>
            <th className="border px-4 py-3 text-center">Selection 1</th>
            <th className="border px-4 py-3 text-center">Selection 2</th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {properties &&
            properties.map((property) => {
              const isSelected1 = selection1 === property.id;
              const isSelected2 = selection2 === property.id;
              return (
                <tr
                  key={property.id}
                  className={`transition-all duration-150 hover:bg-[#F2F2F2] ${
                    isSelected1 || isSelected2 ? 'bg-[#E5F9F8]' : ''
                  }`}
                >
                  <td className="px-4 py-3 border font-mono text-gray-600">
                    {property.code ?? '-'}
                  </td>
                  <td className="px-4 py-3 border text-[#00646C] font-semibold cursor-pointer hover:underline">
                    {property.name}
                  </td>
                  <td className="px-4 py-3 border text-gray-700">
                    {property.description ?? '-'}
                  </td>
                  <td className="px-4 py-3 border text-center">
                    <input
                      type="radio"
                      name="selection1-group"
                      value={property.id}
                      checked={selection1 === property.id}
                      onChange={() =>
                        setSelection1((prev) =>
                          prev === property.id ? null : property.id,
                        )
                      }
                      className="form-radio h-4 w-4 focus:ring-[#AF0061]"
                    />
                  </td>
                  <td className="px-4 py-3 border text-center">
                    <input
                      type="radio"
                      name="selection2-group"
                      value={property.id}
                      checked={selection2 === property.id}
                      onChange={() =>
                        setSelection2((prev) =>
                          prev === property.id ? null : property.id,
                        )
                      }
                      className="form-radio h-4 w-4 focus:ring-[#AF0061]"
                    />
                  </td>
                </tr>
              );
            })}
        </tbody>
      </table>

      <div className="flex justify-between pt-6 mt-6 border-t border-slate-200">
        <Button
          variant="outline"
          onClick={() => {
            setSelection1(null);
            setSelection2(null);
            push('/dashboard/setup/products-services/category');
          }}
        >
          Cancel
        </Button>
        <Button
          variant="ghost"
          type="button"
          onClick={handleReset}
          disabled={isSaving}
        >
          Reset
        </Button>
        <Button
          variant="main"
          type="button"
          onClick={handleSave}
          disabled={isSaving}
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          {isSaving ? 'Saving...' : 'Save & Back to Main'}
        </Button>
      </div>
    </div>
  );
};

interface PropertySelectionProps {
  categoryId: number;
}

const PropertySelection: React.FC<PropertySelectionProps> = ({
  categoryId,
}) => {
  const { data: selectionData, isLoading: isSelectionLoading } = useQuery({
    queryKey: ['category-selection', categoryId],
    queryFn: () => CategoryQuery.getPropertySelections(categoryId),
    enabled: !!categoryId,
  });
  return (
    <Suspense isLoading={isSelectionLoading}>
      <Form defaultValues={selectionData} categoryId={categoryId} />
    </Suspense>
  );
};

export default PropertySelection;
