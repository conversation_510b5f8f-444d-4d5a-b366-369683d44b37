'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import Link from 'next/link';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { modal, DEFAULT_MODAL } from '@/components/ui/overlay';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';
import { generateTableColumns } from '@/lib/tableUtils';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { CompanyInList } from '@/models/Company';
import ContactsTable from './contacts_table/contacts-table';

interface SearchCriteria {
  searchType: 'name' | 'accountNumber' | 'city' | 'email' | 'phone';
  searchValue: string;
  includeArchived: boolean;
}

export const CompanySearch = () => {
  const [searchCriteria, setSearchCriteria] = useState<SearchCriteria>({
    searchType: 'name',
    searchValue: '',
    includeArchived: false,
  });
  const [hasSearched, setHasSearched] = useState(false);
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());

  // Only fetch data when user has searched
  const {
    data: companies,
    isLoading,
    error,
  } = useQuery({
    queryKey: [...CompanyQuery.tags, 'Exhibitor', 'search', searchCriteria],
    queryFn: () => CompanyQuery.getAll('Exhibitor'),
    enabled: hasSearched && searchCriteria.searchValue.trim() !== '',
  });

  const toggleRow = (companyId: number) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(companyId)) {
      newExpandedRows.delete(companyId);
    } else {
      newExpandedRows.add(companyId);
    }
    setExpandedRows(newExpandedRows);
  };

  // Filter companies based on search criteria
  const filteredCompanies =
    companies?.filter((company) => {
      if (!searchCriteria.includeArchived && company.isArchived) {
        return false;
      }

      const searchValue = searchCriteria.searchValue.toLowerCase();
      switch (searchCriteria.searchType) {
        case 'name':
          return company.name.toLowerCase().includes(searchValue);
        case 'accountNumber':
          return company.accountNumber?.toLowerCase().includes(searchValue);
        case 'city':
          return company.city?.toLowerCase().includes(searchValue);
        case 'email':
          return company.email?.toLowerCase().includes(searchValue);
        case 'phone':
          return company.phone?.toLowerCase().includes(searchValue);
        default:
          return false;
      }
    }) || [];

  const columns = generateTableColumns<CompanyInList>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      accountNumber: { name: 'Account Number', type: 'text', sortable: true },
      phone: { name: 'Phone', type: 'text', sortable: true },
      email: { name: 'Email', type: 'text', sortable: true },
      city: { name: 'City', type: 'text', sortable: true },
      isArchived: {
        name: 'Status',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <span className="text-xs bg-gray-200 text-gray-700 px-1.5 py-0.5 rounded-full">
                  Archived
                </span>
              ) : (
                <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded-full">
                  Active
                </span>
              )}
            </div>
          ),
        },
        sortable: true,
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-2 w-full">
              <Button
                variant="outline"
                size="sm"
                onClick={() => toggleRow(row.id)}
                title={
                  expandedRows.has(row.id) ? 'Hide Contacts' : 'Show Contacts'
                }
                iconName="ChevronDownIcon"
                iconProps={{ size: 16 }}
              />
              <Link
                href={`/dashboard/setup/company-contact/exhibitor-company/${row.id}/view`}
              >
                <Button
                  size="sm"
                  variant="outline"
                  iconName="EyeIcon"
                  title="View Details"
                />
              </Link>
              <Link
                href={`/dashboard/setup/company-contact/exhibitor-company/${row.id}`}
              >
                <Button
                  size="sm"
                  variant="secondary"
                  iconName="EditIcon"
                  title="Edit Company"
                />
              </Link>
              <Button
                variant="remove"
                size="sm"
                iconName="RemoveIcon"
                title="Delete Company"
                onClick={() => {
                  modal(
                    ({ close }) => (
                      <MutationConfirmModal
                        close={close}
                        title="Delete Company"
                        description={`Are you sure you want to delete "${row.name}"?`}
                        mutateFn={() => CompanyQuery.delete(row.id!)}
                        mutationKey={[...CompanyQuery.tags]}
                        onSuccess={() => window.location.reload()}
                        variant="destructive"
                        confirmButtonText="Delete"
                        confirmIconName="DeleteIcon"
                        loadingIconName="LoadingIcon"
                      />
                    ),
                    DEFAULT_MODAL,
                  ).open();
                }}
              />
            </div>
          ),
        },
      },
    },
    false,
    false,
    { column: 'name', direction: 'asc' },
  );

  const handleSearch = () => {
    if (searchCriteria.searchValue.trim() === '') {
      return;
    }
    setHasSearched(true);
  };

  const handleReset = () => {
    setSearchCriteria({
      searchType: 'name',
      searchValue: '',
      includeArchived: false,
    });
    setHasSearched(false);
    setExpandedRows(new Set());
  };

  const searchTypeOptions = [
    { value: 'name', label: 'Company Name' },
    { value: 'accountNumber', label: 'Account Number' },
    { value: 'city', label: 'City' },
    { value: 'email', label: 'Email' },
    { value: 'phone', label: 'Phone' },
  ];

  return (
    <div className="space-y-6">
      {/* Search Interface */}
      <Card>
        <CardHeader>
          <CardTitle>Search Exhibitor Companies</CardTitle>
          <CardDescription>
            Select your search criteria and enter a value to find specific
            companies.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="searchType">Search By</Label>
              <Select
                value={searchCriteria.searchType}
                onValueChange={(value: any) =>
                  setSearchCriteria((prev) => ({ ...prev, searchType: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select search type" />
                </SelectTrigger>
                <SelectContent>
                  {searchTypeOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="searchValue">Search Value</Label>
              <Input
                id="searchValue"
                placeholder={`Enter ${searchTypeOptions.find((opt) => opt.value === searchCriteria.searchType)?.label.toLowerCase()}...`}
                value={searchCriteria.searchValue}
                onChange={(e) =>
                  setSearchCriteria((prev) => ({
                    ...prev,
                    searchValue: e.target.value,
                  }))
                }
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSearch();
                  }
                }}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="includeArchived">Options</Label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeArchived"
                  checked={searchCriteria.includeArchived}
                  onCheckedChange={(checked) =>
                    setSearchCriteria((prev) => ({
                      ...prev,
                      includeArchived: !!checked,
                    }))
                  }
                />
                <Label htmlFor="includeArchived" className="text-sm">
                  Include Archived
                </Label>
              </div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={handleSearch}
              disabled={searchCriteria.searchValue.trim() === ''}
              iconName="SearchIcon"
            >
              Search Companies
            </Button>
            <Button
              variant="outline"
              onClick={handleReset}
              iconName="ClearIcon"
            >
              Clear
            </Button>
            <div className="ml-auto">
              <Link href="/dashboard/setup/company-contact/exhibitor-company/add">
                <Button variant="main" iconName="AddIcon">
                  Add New Company
                </Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Section */}
      {hasSearched && (
        <Card>
          <CardHeader>
            <CardTitle>Search Results</CardTitle>
            <CardDescription>
              {isLoading
                ? 'Searching...'
                : `Found ${filteredCompanies.length} companies matching your criteria`}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
              </div>
            ) : filteredCompanies.length > 0 ? (
              <DataTable
                columns={columns}
                data={filteredCompanies}
                expandedRows={expandedRows}
                disableStripedRows
                renderExpandedRow={(row) => (
                  <div className="p-3">
                    <ContactsTable companyId={row.id} companyName={row.name} />
                  </div>
                )}
              />
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">
                  No companies found matching your search criteria.
                </p>
                <Button
                  variant="outline"
                  onClick={handleReset}
                  className="mt-4"
                  iconName="ClearIcon"
                >
                  Clear Search
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CompanySearch;
