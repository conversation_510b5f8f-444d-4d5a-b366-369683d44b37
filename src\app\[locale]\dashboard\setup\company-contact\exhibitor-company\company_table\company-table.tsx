'use client';

import { useQuery } from '@tanstack/react-query';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { FaPlus } from 'react-icons/fa';
import Link from 'next/link';
import { useState } from 'react';
import { DataTable } from '@/components/ui/data-table';
import { modal, DEFAULT_MODAL } from '@/components/ui/overlay';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { Button } from '@/components/ui/button';
import { CompanyInList } from '@/models/Company';
import { ProvinceQuery } from '@/services/queries/ProvinceQuery';
import { CountryQuery } from '@/services/queries/CountryQuery';
import { ContactsTable } from '../components/contacts_table/contacts-table';

const CompanyTable = () => {
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());

  const { data, isLoading } = useQuery({
    queryKey: [...CompanyQuery.tags, 'Show manager'],
    queryFn: () => CompanyQuery.getAll('Show manager'),
  });

  const toggleRow = (companyId: number) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(companyId)) {
      newExpanded.delete(companyId);
    } else {
      newExpanded.add(companyId);
    }
    setExpandedRows(newExpanded);
  };

  const { data: provinces } = useQuery({
    queryKey: ProvinceQuery.tags,
    queryFn: ProvinceQuery.getAll,
  });

  const { data: countries } = useQuery({
    queryKey: CountryQuery.tags,
    queryFn: CountryQuery.getAll,
  });

  const columns = generateTableColumns<CompanyInList>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      city: { name: 'City', type: 'text', sortable: true },
      province: { name: 'Province', type: 'text', sortable: true },
      country: { name: 'Country', type: 'text', sortable: true },
      phone: { name: 'Phone', type: 'text', sortable: true },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-2 w-full justify-center">
              <Button
                variant="outline"
                size="icon"
                onClick={() => toggleRow(row.id)}
                title={
                  expandedRows.has(row.id) ? 'Hide Contacts' : 'Show Contacts'
                }
              >
                {expandedRows.has(row.id) ? (
                  <ChevronDown className="size-4" />
                ) : (
                  <ChevronRight className="size-4" />
                )}
              </Button>
              <Link href={`/dashboard/setup/master-setup/company/${row.id}`}>
                <Button
                  size="sm"
                  variant="secondary"
                  iconName="EditIcon"
                ></Button>
              </Link>
              <Button
                variant="remove"
                size="sm"
                iconName="RemoveIcon"
                onClick={() => {
                  modal(
                    ({ close }) => (
                      <MutationConfirmModal
                        close={close}
                        title="Delete Company"
                        description={`Are you sure you want to delete "${row.name}"?`}
                        mutateFn={() => CompanyQuery.delete(row.id!)}
                        mutationKey={[...CompanyQuery.tags]}
                        onSuccess={() => window.location.reload()}
                        variant="destructive"
                        confirmButtonText="Delete"
                        confirmIconName="DeleteIcon"
                        loadingIconName="LoadingIcon"
                      />
                    ),
                    DEFAULT_MODAL,
                  ).open();
                }}
              ></Button>
            </div>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<CompanyInList>({
    name: {
      name: 'Name',
      type: 'text',
    },
    province: {
      name: 'Province',
      type: {
        type: 'select',
        options:
          provinces?.map((province) => ({
            label: province.name,
            value: province.name,
          })) || [],
      },
    },
    country: {
      name: 'Country',
      type: {
        type: 'select',
        options:
          countries?.map((country) => ({
            label: country.name,
            value: country.name,
          })) || [],
      },
    },
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      filterFields={filters}
      isLoading={isLoading}
      expandedRows={expandedRows}
      renderExpandedRow={(row) => (
        <div className="p-3 bg-brand-brown/15 border-t-2 border-brand-brown/40">
          <div className="bg-white rounded-lg shadow-sm border border-brand-brown/30 p-4">
            <ContactsTable companyId={row.id} companyName={row.name} />
          </div>
        </div>
      )}
      controls={
        <div className="flex flex-row gap-2 justify-end">
          <Link href="/dashboard/setup/master-setup/company/add">
            <Button variant="main">
              <FaPlus className="mr-2 h-4 w-4" />
              Add New Company
            </Button>
          </Link>
        </div>
      }
    />
  );
};

export default CompanyTable;
