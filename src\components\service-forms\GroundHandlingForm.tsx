'use client';

import React from 'react';
import { BaseServiceForm } from './BaseServiceForm';
import {
  GroundHandlingSchema,
  GroundHandlingFormType,
} from '@/schema/ServiceFormSchemas';
import { ServiceFormType } from '@/models/Service';
import Field from '@/components/ui/inputs/field';

interface GroundHandlingFormProps {
  defaultValues?: GroundHandlingFormType;
  onSubmit: (data: GroundHandlingFormType) => void;
  onCancel?: () => void;
  isLoading?: boolean;
}

export function GroundHandlingForm({
  defaultValues,
  onSubmit,
  onCancel,
  isLoading,
}: GroundHandlingFormProps) {
  const equipmentOptions = [
    { label: 'Forklift', value: 'forklift' },
    { label: 'Pallet Jack', value: 'pallet_jack' },
    { label: 'Hand Truck', value: 'hand_truck' },
    { label: 'Crane', value: 'crane' },
    { label: 'Loading Dock', value: 'loading_dock' },
    { label: 'Conveyor Belt', value: 'conveyor_belt' },
  ];

  return (
    <BaseServiceForm
      schema={GroundHandlingSchema}
      defaultValues={defaultValues}
      onSubmit={onSubmit}
      onCancel={onCancel}
      isLoading={isLoading}
      serviceType={ServiceFormType.GROUND_HANDLING}
    >
      {(form) => (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Field
              control={form.control}
              name="equipmentRequired"
              label="Equipment Required"
              type={{
                type: 'select',
                props: {
                  options: equipmentOptions,
                  placeholder: 'Select equipment needed',
                  multiple: true,
                },
              }}
              required
            />

            <Field
              control={form.control}
              name="numberOfWorkers"
              label="Number of Workers"
              type={{
                type: 'number',
                props: {
                  placeholder: 'Enter number of workers',
                  min: 1,
                },
              }}
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Field
              control={form.control}
              name="workingHours.startTime"
              label="Start Time"
              type={{
                type: 'time',
                props: {
                  placeholder: 'Select start time',
                },
              }}
              required
            />

            <Field
              control={form.control}
              name="workingHours.endTime"
              label="End Time"
              type={{
                type: 'time',
                props: {
                  placeholder: 'Select end time',
                },
              }}
              required
            />
          </div>

          <Field
            control={form.control}
            name="specialRequirements"
            label="Special Requirements"
            type={{
              type: 'textarea',
              props: {
                placeholder: 'Enter any special requirements or notes',
                rows: 3,
              },
            }}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Field
              control={form.control}
              name="contactPerson"
              label="Contact Person"
              type={{
                type: 'text',
                props: {
                  placeholder: 'Enter contact person name',
                },
              }}
              required
            />

            <Field
              control={form.control}
              name="contactPhone"
              label="Contact Phone"
              type={{
                type: 'tel',
                props: {
                  placeholder: 'Enter contact phone number',
                },
              }}
              required
            />
          </div>

          <Field
            control={form.control}
            name="estimatedCost"
            label="Estimated Cost ($)"
            type={{
              type: 'number',
              props: {
                placeholder: 'Enter estimated cost',
                min: 0,
                step: 0.01,
              },
            }}
            required
          />
        </>
      )}
    </BaseServiceForm>
  );
}
