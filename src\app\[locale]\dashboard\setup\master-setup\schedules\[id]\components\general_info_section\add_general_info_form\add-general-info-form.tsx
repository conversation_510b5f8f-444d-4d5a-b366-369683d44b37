'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import { ScheduleSchema, ScheduleData } from '@/schema/ScheduleSchema';
import AuthQuery from '@/services/queries/AuthQuery';
import ScheduleQuery from '@/services/queries/ScheduleQuery';
import { getQueryClient } from '@/utils/query-client';
import Field from '@/components/ui/inputs/field';

function FormContent({
  defaultValues,
  id,
}: {
  id?: number;
  defaultValues?: ScheduleData;
}) {
  const { toast } = useToast();
  const { push } = useRouter();

  const { mutate, isPending } = useMutation({
    mutationFn: id ? ScheduleQuery.update(id) : ScheduleQuery.create,
    onSuccess: async (data) => {
      if (Number.isInteger(data))
        push('/dashboard/setup/master-setup/schedules/' + data);
      else {
        await getQueryClient().invalidateQueries({
          queryKey: [...ScheduleQuery.tags, { id }],
        });
      }
      await getQueryClient().invalidateQueries({
        queryKey: [AuthQuery.tags.me],
      });

      await getQueryClient().invalidateQueries({
        queryKey: [...ScheduleQuery.tags],
      });
      toast({
        variant: 'success',
        title: Number.isInteger(data) ? 'Schedule created' : 'Schedule updated',
      });
    },
  });

  const form = useForm<ScheduleData>({
    resolver: zodResolver(ScheduleSchema),
    defaultValues: defaultValues
      ? {
          name: defaultValues.name,
          description: defaultValues.description,
          notes: defaultValues.notes,
          isActive: defaultValues.isActive,
        }
      : {
          name: '',
          description: '',
          notes: '',
          isActive: true,
        },
  });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => mutate(data))}
        className="flex flex-col gap-6 w-full rounded-[12px]"
      >
        <div className="grid md:grid-cols-2 gap-6">
          <Field
            control={form.control}
            name="name"
            label="Name"
            required={true}
            type="text"
            placeholder="Enter schedule name"
          />
          <Field
            control={form.control}
            name="description"
            label="Description"
            required={true}
            type="text"
            placeholder="Enter schedule description"
          />
        </div>

        <div className="grid md:grid-cols-1 gap-6">
          <Field
            control={form.control}
            name="notes"
            label="Notes"
            type="textarea"
            placeholder="Enter additional notes (optional)"
            containerClassName="max-w-2xl"
          />
        </div>

        <div className="grid md:grid-cols-1 gap-6">
          <Field
            control={form.control}
            name="isActive"
            label="Active Status"
            type="checkbox"
            description="Enable this schedule for use"
            containerClassName="max-w-md"
          />
        </div>
        <div className="flex justify-between pt-6 border-t border-slate-200">
          <Button
            type="button"
            variant="outline"
            onClick={() => push('/dashboard/setup/master-setup/schedules')}
          >
            Cancel
          </Button>
          <Button
            variant={'main'}
            disabled={isPending}
            iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
            iconProps={{ className: isPending ? 'animate-spin' : '' }}
          >
            {isPending ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </form>
    </Form>
  );
}

interface IContent {
  id?: number;
}

export default function Content({ id }: IContent) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: [...ScheduleQuery.tags, { id }],
    queryFn: () => ScheduleQuery.get(id!),
    enabled: id != undefined,
    select: (d) => {
      return {
        name: d.name,
        description: d.description,
        notes: d.notes || '',
        isActive: d.isActive,
      };
    },
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <FormContent id={Number(id)} defaultValues={data} />
    </Suspense>
  );
}
