import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import UnderDevelopment from './UnderDevelopment';

export default function ShowManagement({ show }: { show: any }) {
  if (!show || !show.showManagement) {
    return <UnderDevelopment sectionName="Show Management" />;
  }

  const { manager, billingContact } = show.showManagement;

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Show Management</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div>
            <h3 className="font-medium text-slate-800 mb-2">Manager</h3>
            <div className="space-y-1 text-sm">
              <p className="font-medium">Name: {manager.name}</p>
              <p>Company: {manager.company}</p>
              <p>Email: {manager.email}</p>
              <p>Tel: {manager.tel}</p>
            </div>
          </div>
          <div>
            <h3 className="font-medium text-slate-800 mb-2">Billing Contact</h3>
            <div className="space-y-1 text-sm">
              <p className="font-medium">Name: {billingContact.name}</p>
              <p>Company: {billingContact.company}</p>
              <p>Email: {billingContact.email}</p>
              <p>Tel: {billingContact.tel}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
