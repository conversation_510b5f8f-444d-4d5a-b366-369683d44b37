import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ShowPromoter } from '@/models/Show';

export default function ShowManagement({
  promoter,
}: {
  promoter?: ShowPromoter;
}) {
  if (!promoter) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Show Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-slate-500 text-center py-4">
            No company information available
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Show Management</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div>
            <h3 className="font-medium text-slate-800 mb-2">
              Company Information
            </h3>
            <div className="space-y-2">
              <div>
                <span className="font-medium text-slate-700">
                  Company Name:
                </span>
                <span className="ml-2">{promoter.companyName}</span>
              </div>
              <div>
                <span className="font-medium text-slate-700">Show:</span>
                <span className="ml-2">
                  {promoter.showName} ({promoter.showCode})
                </span>
              </div>
              <div>
                <span className="font-medium text-slate-700">
                  Show Subcontact:
                </span>
                <span className="ml-2">
                  {promoter.showSubcontact ? 'Yes' : 'No'}
                </span>
              </div>
              <div>
                <span className="font-medium text-slate-700">
                  Floor Plan Required:
                </span>
                <span className="ml-2">
                  {promoter.floorPlanRequired ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
          </div>

          {promoter.taxes && promoter.taxes.length > 0 && (
            <div>
              <h3 className="font-medium text-slate-800 mb-2">
                Tax Information
              </h3>
              <div className="space-y-1">
                {promoter.taxes.map((tax) => (
                  <div key={tax.id} className="text-sm">
                    <span className="font-medium">
                      {tax.taxTypeName} ({tax.taxTypeAbbreviation}):
                    </span>
                    <span className="ml-1">
                      {tax.taxRate}% - {tax.provinceName}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
