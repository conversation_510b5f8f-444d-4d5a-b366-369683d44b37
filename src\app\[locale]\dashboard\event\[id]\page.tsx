import type { Metadata } from 'next';
import ShowQuery from '@/services/queries/ShowQuery';
import EventPageClient from './EventPageClient';

type Props = {
  params: { id: string };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const show = await ShowQuery.getOne(Number(params.id));

  return {
    title: `${show.name} | GOODKEY SHOW SERVICES LTD.`,
    description: `View details for ${show.name} event`,
  };
}

export default async function EventPage(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;
  return <EventPageClient params={params} />;
}
