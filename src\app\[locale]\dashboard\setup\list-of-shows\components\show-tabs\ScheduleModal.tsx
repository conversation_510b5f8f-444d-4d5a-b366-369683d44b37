import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import ShowQuery from '@/services/queries/ShowQuery';
import { ShowSchedule } from '@/models/Show';
import { z } from 'zod';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import Suspense from '@/components/ui/Suspense';
import { modal } from '@/components/ui/overlay';
import { format, parse } from 'date-fns';
import { ShowScheduleSchema } from '@/schema/ShowScheduleSchema';

export type ShowScheduleFormType = z.infer<typeof ShowScheduleSchema>;

function FormContent({
  showId,
  schedule,
}: {
  showId: number;
  schedule?: ShowSchedule;
}) {
  const isEdit = !!schedule;
  const queryClient = useQueryClient();
  const { toast } = useToast();
  // Helper to convert 'HH:mm:ss' string to Date
  function timeStringToDate(timeStr: string | undefined): Date | undefined {
    if (!timeStr) return undefined;
    const [h, m, s] = timeStr.split(':').map(Number);
    const d = new Date();
    d.setHours(h || 0, m || 0, s || 0, 0);
    return d;
  }
  // Helper to convert Date to 'HH:mm:ss' string
  function dateToTimeString(date: Date | undefined): string {
    if (!date) return '';
    return date.toLocaleTimeString('en-GB', { hour12: false }).split(' ')[0];
  }
  // Helper to create a Date at a specific time (today)
  function createTimeDate(
    hours: number,
    minutes: number,
    seconds: number = 0,
  ): Date {
    const d = new Date();
    d.setHours(hours, minutes, seconds, 0);
    return d;
  }
  // Helper to parse 'YYYY-MM-DD' to local Date
  function parseDateStringToLocal(dateStr: string): Date {
    return parse(dateStr, 'yyyy-MM-dd', new Date());
  }
  // Helper to format Date to 'YYYY-MM-DD' in local time
  function formatDateToStringLocal(date: Date): string {
    return format(date, 'yyyy-MM-dd');
  }
  const todayDate = new Date();
  const form = useForm<ShowScheduleFormType>({
    resolver: zodResolver(ShowScheduleSchema),
    defaultValues: schedule
      ? {
          showScheduleDate: parseDateStringToLocal(schedule.showScheduleDate),
          timeStart: timeStringToDate(schedule.timeStart),
          timeEnd: timeStringToDate(schedule.timeEnd),
          showScheduleConfirmed: schedule.showScheduleConfirmed,
          showScheduleComments: schedule.showScheduleComments || '',
          applyScheduleToServiceForm: schedule.applyScheduleToServiceForm,
        }
      : {
          showScheduleDate: todayDate,
          timeStart: createTimeDate(9, 0, 0), // 09:00:00
          timeEnd: createTimeDate(17, 0, 0), // 17:00:00
          showScheduleConfirmed: false,
          showScheduleComments: '',
          applyScheduleToServiceForm: false,
        },
  });
  const mutation = useMutation({
    mutationFn: async (data: ShowScheduleFormType) => {
      const payload = {
        ...data,
        showScheduleDate: formatDateToStringLocal(
          data.showScheduleDate as Date,
        ),
        timeStart: dateToTimeString(data.timeStart as Date),
        timeEnd: dateToTimeString(data.timeEnd as Date),
      };
      if (isEdit && schedule) {
        await ShowQuery.updateSchedule(showId, schedule.id)(payload);
      } else {
        await ShowQuery.createSchedule(showId, payload);
      }
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: ['show-schedule', schedule?.id],
      });
      await queryClient.invalidateQueries({
        queryKey: ['show-schedules', showId],
      });
      toast({
        title: isEdit ? 'Schedule updated' : 'Schedule added',
        variant: 'success',
      });
      modal.close();
    },
    onError: (e: any) =>
      toast({ title: e.message || 'Failed to save', variant: 'destructive' }),
  });
  return (
    <Form {...form}>
      <ModalContainer
        title={isEdit ? 'Edit Schedule' : 'Add Schedule'}
        description={isEdit ? 'Update schedule details' : 'Create new schedule'}
        onSubmit={form.handleSubmit((data) => mutation.mutate(data))}
        controls={
          <div className="flex justify-end gap-2 mt-4">
            <Button
              variant="main"
              disabled={mutation.isPending}
              type="submit"
              iconName={
                mutation.isPending
                  ? 'LoadingIcon'
                  : isEdit
                    ? 'SaveIcon'
                    : 'AddIcon'
              }
              iconProps={{ size: isEdit ? 16 : 20, className: '' }}
            >
              {isEdit ? 'Update' : 'Create'}
            </Button>
          </div>
        }
      >
        <div className="p-4 space-y-4">
          <Field
            control={form.control}
            name="showScheduleDate"
            label="Date"
            type="date"
            required
          />
          <Field
            control={form.control}
            name="timeStart"
            label="Start Time"
            type={{ type: 'time' }}
            required
          />
          <Field
            control={form.control}
            name="timeEnd"
            label="End Time"
            type={{ type: 'time' }}
            required
          />
          <Field
            control={form.control}
            name="showScheduleConfirmed"
            label="Confirmed"
            type="checkbox"
          />
          <Field
            control={form.control}
            name="applyScheduleToServiceForm"
            label="Apply to Service Form"
            type="checkbox"
          />
          <Field
            control={form.control}
            name="showScheduleComments"
            label="Comments"
            type="textarea"
          />
        </div>
      </ModalContainer>
    </Form>
  );
}

function ScheduleModal({
  showId,
  scheduleId,
}: {
  showId: number;
  scheduleId?: number;
}) {
  const { data: fetchedSchedule, isLoading } = useQuery({
    queryKey: ['show-schedule', scheduleId],
    queryFn: () => ShowQuery.getSchedule(scheduleId!),
    enabled: !!scheduleId,
  });
  return (
    <Suspense isLoading={!!scheduleId && isLoading}>
      <FormContent showId={showId} schedule={fetchedSchedule} />
    </Suspense>
  );
}

export default ScheduleModal;
