'use client';
import { But<PERSON> } from '@/components/ui/button';
import { EventSidebar } from '@/components/ui/event-sidebar';
import ShowQuery from '@/services/queries/ShowQuery';
import EventInformation from './components/EventInformation';
import ShowManagement from './components/ShowManagement';
import Notices from './components/Notices';
import WorkorderLocations from './components/WorkorderLocations';
import ShowDetails from './components/ShowDetails';
import Documents from './components/Documents';
import Schedule from './components/Schedule';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';
import { XCircleIcon } from '@/assets/Icons';
import { ShowSchedule, ShowPromoter } from '@/models/Show'; // Import Show and ShowPromoter
import { ShowLocationInList } from '@/models/ShowLocation'; // Import ShowLocationInList

// New imports for promoter and hall contact components
import PromoterInfo from './components/PromoterInfo';
import HallContactInfo from './components/HallContactInfo';

// Define HallContact interface based on HallContactInfo.tsx usage
interface HallContact {
  hallName: string;
  contactName: string;
  contactEmail: string;
  contactPhone: string;
  // Add other properties if they exist in the API response for hallContact
}

export default function EventPageClient({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const showId = Number(params.id);

  const {
    data: show,
    isLoading: isLoadingShow,
    isError: isShowError,
    error: showError,
  } = useQuery({
    queryKey: [ShowQuery.tags, showId],
    queryFn: () => ShowQuery.getOne(showId),
  });

  const {
    data: schedules,
    isLoading: isLoadingSchedules,
    isError: isSchedulesError,
    error: schedulesError,
  } = useQuery<ShowSchedule[], Error>({
    queryKey: [ShowQuery.tags, 'schedules', showId],
    queryFn: () => ShowQuery.getSchedules(showId),
    enabled: !!showId,
  });

  const {
    data: promoter,
    isLoading: isLoadingPromoter,
    isError: isPromoterError,
    error: promoterError,
  } = useQuery<ShowPromoter, Error>({
    queryKey: [ShowQuery.tags, 'promoter', showId],
    queryFn: () => ShowQuery.getPromoter(showId),
    enabled: !!showId,
  });

  const {
    data: hallContact,
    isLoading: isLoadingHallContact,
    isError: isHallContactError,
    error: hallContactError,
  } = useQuery<HallContact, Error>({
    queryKey: [ShowQuery.tags, 'hallContact', showId],
    queryFn: () => ShowQuery.getHallContact(showId),
    enabled: !!showId,
  });

  const {
    data: locations,
    isLoading: isLoadingLocations,
    isError: isLocationsError,
    error: locationsError,
  } = useQuery<ShowLocationInList[], Error>({
    queryKey: [ShowLocationQuery.tags],
    queryFn: () => ShowLocationQuery.getAll(),
  });

  const isLoading =
    isLoadingShow ||
    isLoadingLocations ||
    isLoadingSchedules ||
    isLoadingPromoter ||
    isLoadingHallContact;

  const isError =
    isShowError ||
    isSchedulesError ||
    isPromoterError ||
    isHallContactError ||
    isLocationsError;

  const errorMessage =
    showError?.message ||
    schedulesError?.message ||
    promoterError?.message ||
    hallContactError?.message ||
    locationsError?.message ||
    'Failed to load event details';

  const locationName = locations?.find(
    (location) => location.id == Number(show?.locationId),
  )?.name;

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00646C] mx-auto mb-4"></div>
          <p className="text-slate-600">Loading event details...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <XCircleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 font-medium mb-2">
            Error Loading Event Details
          </p>
          <p className="text-slate-600 text-sm">{errorMessage}</p>
          <Button
            variant="outline"
            size="sm"
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-6 flex items-center justify-between">
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => router.back()}>
            Back to Events
          </Button>
          <Button
            className="bg-[#00646C] hover:bg-[#00646C]/90 text-white"
            onClick={() =>
              router.push(`/dashboard/event/${params.id}/products`)
            }
          >
            Order Online
          </Button>
        </div>
      </div>

      <EventInformation show={show} locationName={locationName} />

      <div className="flex flex-col md:flex-row gap-6">
        <div className="w-full md:w-64 mb-6 md:mb-0">
          <EventSidebar eventId={params.id} activeItem="SHOW MANAGEMENT" />
        </div>

        <div className="flex-1">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <ShowManagement show={show} />
            <Notices show={show} />
          </div>

          {/* New sections for Promoter and Hall Contact */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <PromoterInfo promoter={promoter} />
            <HallContactInfo hallContact={hallContact} />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <WorkorderLocations show={show} />
            <ShowDetails show={show} />
          </div>

          <Documents show={show} />

          {/* Pass schedules to the Schedule component */}
          <Schedule show={show} schedules={schedules} />
        </div>
      </div>
    </div>
  );
}
