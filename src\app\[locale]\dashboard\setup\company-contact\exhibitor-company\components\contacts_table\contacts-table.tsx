'use client';

import { useQuery } from '@tanstack/react-query';
import Link from 'next/link';
import { DataTable } from '@/components/ui/data-table';
import { modal, DEFAULT_MODAL } from '@/components/ui/overlay';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { Button } from '@/components/ui/button';
import { ContactInList } from '@/models/Contact';
import ContactTypeQuery from '@/services/queries/ContactTypeQuery';
import { getQueryClient } from '@/utils/query-client';

interface ContactsTableProps {
  companyId: number;
  companyName: string;
}

export const ContactsTable = ({
  companyId,
  companyName,
}: ContactsTableProps) => {
  const { data: contacts } = useQuery({
    queryKey: [...CompanyQuery.tags, 'contacts', companyId],
    queryFn: () => CompanyQuery.contacts.getAll(companyId),
  });

  const { data: contactTypes } = useQuery({
    queryKey: ContactTypeQuery.tags,
    queryFn: ContactTypeQuery.getAll,
  });

  const columns = generateTableColumns<ContactInList>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      fullName: { name: 'Full Name', type: 'text', sortable: true },
      contactType: { name: 'Contact Type', type: 'text', sortable: true },
      email: { name: 'Email', type: 'text', sortable: true },
      telephone: { name: 'Phone', type: 'text', sortable: true },
      cellphone: { name: 'Cell Phone', type: 'text', sortable: true },
      isArchived: {
        name: 'Status',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <span className="text-xs bg-gray-200 text-gray-700 px-1.5 py-0.5 rounded-full">
                  Archived
                </span>
              ) : (
                <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded-full">
                  Active
                </span>
              )}
            </div>
          ),
        },
        sortable: true,
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-1 w-full">
              <Link
                href={`/dashboard/setup/company-contact/exhibitor-company/${companyId}/contacts/${row.id}`}
              >
                <Button
                  size="sm"
                  variant="secondary"
                  iconName="EditIcon"
                ></Button>
              </Link>
              <Button
                variant="remove"
                size="sm"
                iconName="RemoveIcon"
                onClick={() => {
                  modal(
                    ({ close }) => (
                      <MutationConfirmModal
                        close={close}
                        title="Delete Contact"
                        description={`Are you sure you want to delete "${row.fullName}"?`}
                        mutateFn={() =>
                          CompanyQuery.contacts.delete(companyId, row.id)
                        }
                        mutationKey={[
                          ...CompanyQuery.tags,
                          'contacts',
                          companyId,
                        ]}
                        onSuccess={async () => {
                          await getQueryClient().invalidateQueries({
                            queryKey: [
                              ...CompanyQuery.tags,
                              'contacts',
                              companyId,
                            ],
                          });
                        }}
                        variant="destructive"
                        confirmButtonText="Delete"
                        confirmIconName="DeleteIcon"
                        loadingIconName="LoadingIcon"
                      />
                    ),
                    DEFAULT_MODAL,
                  ).open();
                }}
              ></Button>
            </div>
          ),
        },
      },
    },
    false,
    false,
    { column: 'fullName', direction: 'asc' },
  );

  const filters = generateTableFilters<ContactInList>({
    fullName: {
      name: 'Name',
      type: 'text',
    },
    contactType: {
      name: 'Contact Type',
      type: 'select',
      options:
        contactTypes?.map((type) => ({
          label: type.name,
          value: type.name,
        })) || [],
    },
    email: {
      name: 'Email',
      type: 'text',
    },
    isArchived: {
      name: 'Status',
      type: 'select',
      options: [
        { label: 'Active', value: 'false' },
        { label: 'Archived', value: 'true' },
      ],
    },
  });

  return (
    <DataTable
      columns={columns}
      data={contacts || []}
      filters={filters}
      disableAlternatingRows={true}
    />
  );
};

export default ContactsTable;
