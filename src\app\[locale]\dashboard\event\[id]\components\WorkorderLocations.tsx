import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import UnderDevelopment from './UnderDevelopment';
import { ShowGeneralInfoData } from '@/app/[locale]/dashboard/setup/list-of-shows/components/show-tabs/GeneralInfoTab';

export default function WorkorderLocations({
  show,
}: {
  show?: ShowGeneralInfoData;
}) {
  if (!show || !show.workorderLocations) {
    return <UnderDevelopment sectionName="Workorder Locations" />;
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Workorder Locations</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {show.workorderLocations.map((location: any, index: number) => (
            <div key={index} className="border rounded-md p-3">
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-medium text-slate-800">{location.name}</h3>
              </div>
              {location.details && (
                <p className="text-sm text-slate-600">{location.details}</p>
              )}
              <div className="flex gap-2 mt-2">
                <Button variant="outline" size="sm" className="w-full">
                  Modify
                </Button>
                <Button variant="outline" size="sm" className="w-full">
                  Subdivisions
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
