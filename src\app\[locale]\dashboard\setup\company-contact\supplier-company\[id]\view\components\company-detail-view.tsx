'use client';

import { useQuery } from '@tanstack/react-query';
import Link from 'next/link';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { Button } from '@/components/ui/button';
import CardGenerator from '@/components/ui/card_generator/card-generator';
import { ContactsTable } from '../../../components/contacts_table/contacts-table';

interface CompanyDetailViewProps {
  companyId: number;
}

export default function CompanyDetailView({
  companyId,
}: CompanyDetailViewProps) {
  const {
    data: company,
    isLoading,
    error,
  } = useQuery({
    queryKey: [...CompanyQuery.tags, { id: companyId }],
    queryFn: () => CompanyQuery.getOne(companyId),
  });

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (error || !company) {
    return (
      <CardGenerator
        title="Company Not Found"
        description="The company you're looking for doesn't exist or has been deleted."
      >
        <div className="text-center py-8">
          <Link href="/dashboard/setup/company-contact/supplier-company">
            <Button variant="outline" iconName="BackIcon">
              Back to Companies
            </Button>
          </Link>
        </div>
      </CardGenerator>
    );
  }

  return (
    <div className="space-y-4">
      {/* Company Information Card */}
      <CardGenerator
        title={
          <div className="flex justify-between items-center w-full">
            <div>
              <h3 className="text-lg font-semibold">{`${company.name} ${company.isArchived ? '(Archived)' : ''}`}</h3>
              <p className="text-sm text-gray-500">{`Company ID: ${company.id} • Supplier Company`}</p>
            </div>
            <Link
              href={`/dashboard/setup/company-contact/supplier-company/${companyId}`}
            >
              <Button variant="outline" iconName="EditIcon">
                Edit Company
              </Button>
            </Link>
          </div>
        }
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div>
            <label className="text-sm font-medium text-gray-500">
              Company Name
            </label>
            <p className="text-gray-900 font-medium">{company.name}</p>
          </div>

          {company.accountNumber && (
            <div>
              <label className="text-sm font-medium text-gray-500">
                Account Number
              </label>
              <p className="text-gray-900">{company.accountNumber}</p>
            </div>
          )}

          <div>
            <label className="text-sm font-medium text-gray-500">
              Company Group
            </label>
            <p className="text-gray-900">Supplier</p>
          </div>

          {company.phone && (
            <div>
              <label className="text-sm font-medium text-gray-500">Phone</label>
              <p className="text-gray-900">{company.phone}</p>
            </div>
          )}

          {company.email && (
            <div>
              <label className="text-sm font-medium text-gray-500">Email</label>
              <p className="text-gray-900">{company.email}</p>
            </div>
          )}

          {company.websiteUrl && (
            <div>
              <label className="text-sm font-medium text-gray-500">
                Website
              </label>
              <a
                href={company.websiteUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 underline"
              >
                {company.websiteUrl}
              </a>
            </div>
          )}

          {company.address1 && (
            <div className="md:col-span-2 lg:col-span-3">
              <label className="text-sm font-medium text-gray-500">
                Address
              </label>
              <p className="text-gray-900">
                {company.address1}
                {company.address2 && (
                  <>
                    <br />
                    {company.address2}
                  </>
                )}
                {company.city && (
                  <>
                    <br />
                    {company.city}
                    {company.postalCode && `, ${company.postalCode}`}
                  </>
                )}
              </p>
            </div>
          )}

          {company.note && (
            <div className="md:col-span-2 lg:col-span-3">
              <label className="text-sm font-medium text-gray-500">Notes</label>
              <p className="text-gray-900">{company.note}</p>
            </div>
          )}

          <div>
            <label className="text-sm font-medium text-gray-500">Status</label>
            <div className="flex items-center space-x-2">
              <div
                className={`w-3 h-3 rounded-full ${
                  company.isArchived ? 'bg-gray-400' : 'bg-green-500'
                }`}
              ></div>
              <span
                className={`font-medium ${
                  company.isArchived ? 'text-gray-600' : 'text-green-600'
                }`}
              >
                {company.isArchived ? 'Archived' : 'Active'}
              </span>
            </div>
          </div>
        </div>
      </CardGenerator>

      {/* Contacts Section */}
      <CardGenerator 
        title={
          <div className="flex justify-between items-center w-full">
            <h3 className="text-lg font-semibold">Company Contacts</h3>
            <Link
              href={`/dashboard/setup/company-contact/supplier-company/${companyId}/contacts/add`}
            >
              <Button variant="main" iconName="AddIcon">
                Add Contact
              </Button>
            </Link>
          </div>
        }
      >
        <ContactsTable companyId={companyId} companyName={company.name} />
      </CardGenerator>
    </div>
  );
}
