import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import UnderDevelopment from './UnderDevelopment';
import { ShowGeneralInfoData } from '@/app/[locale]/dashboard/setup/list-of-shows/components/show-tabs/GeneralInfoTab';

export default function ShowDetails({ show }: { show?: ShowGeneralInfoData }) {
  if (!show || !show.showDetails) {
    return <UnderDevelopment sectionName="Show Details" />;
  }

  const { showDetails } = show;

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Show Details</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div className="flex justify-between col-span-2">
            <span className="font-medium">Number of Booths:</span>
            <span>{showDetails.numberOfBooths}</span>
          </div>
          <div className="flex justify-between col-span-2">
            <span className="font-medium">Booth Numbers:</span>
            <span>{showDetails.boothNumbers}</span>
          </div>
          <div className="flex justify-between col-span-2">
            <span className="font-medium">Booths:</span>
            <span>{showDetails.booths}</span>
          </div>
          <div className="flex justify-between col-span-1">
            <span className="font-medium">Chairs:</span>
            <span>{showDetails.chairs || '—'}</span>
          </div>
          <div className="flex justify-between col-span-1">
            <span className="font-medium">Signs:</span>
            <span>{showDetails.signs || '—'}</span>
          </div>
          <div className="flex justify-between col-span-1">
            <span className="font-medium">Tables:</span>
            <span>{showDetails.tables || '—'}</span>
          </div>
          <div className="flex justify-between col-span-1">
            <span className="font-medium">Tables Color:</span>
            <span>{showDetails.tablesColour || '—'}</span>
          </div>
          <div className="flex justify-between col-span-1">
            <span className="font-medium">Drape Back Wall:</span>
            <span>{showDetails.drapeColourBackWall || '—'}</span>
          </div>
          <div className="flex justify-between col-span-1">
            <span className="font-medium">Drape Side Wall:</span>
            <span>{showDetails.drapeColourSideWall || '—'}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
