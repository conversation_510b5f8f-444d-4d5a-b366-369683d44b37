import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { getQueryClient } from '@/utils/query-client';
import ContactForm from './components/contact_form/contact-form';
import CompanyInfoCard from './components/company_info_card';
import AppLayout from '@/components/ui/app_layout';

export default async function ContactPage({
  params,
}: {
  params: Promise<{ id: string; contactId: string }>;
}) {
  try {
    const resolvedParams = await params;
    const { id, contactId } = resolvedParams;
    const isAdd = contactId === 'add';

    const client = getQueryClient();

    // Validate companyId
    if (Number.isNaN(Number(id))) throw new Error();

    // Prefetch company data for breadcrumbs
    await client.prefetchQuery({
      queryKey: [...CompanyQuery.tags, { id: Number(id) }],
      queryFn: () => CompanyQuery.getOne(Number(id)),
    });

    // Prefetch contact data if editing
    if (!isAdd) {
      if (Number.isNaN(Number(contactId))) throw new Error();
      await client.prefetchQuery({
        queryKey: [
          ...CompanyQuery.tags,
          'contacts',
          Number(id),
          Number(contactId),
        ],
        queryFn: () =>
          CompanyQuery.contacts.getOne(Number(id), Number(contactId)),
      });
    }

    const breadcrumbItems = [
      { title: 'Setup', link: '/dashboard/setup' },
      {
        title: 'Company-Contact',
        link: '/dashboard/setup/company-contact/supplier-company',
      },
      {
        title: 'Supplier Companies',
        link: '/dashboard/setup/company-contact/supplier-company',
      },
      {
        title: isAdd ? 'Add Contact' : 'Edit Contact',
        link: `/dashboard/setup/company-contact/supplier-company/${id}/contacts/${contactId}`,
      },
    ];

    return (
      <AppLayout items={breadcrumbItems}>
        <HydrationBoundary state={dehydrate(client)}>
          <div className="space-y-6">
            <CompanyInfoCard companyId={Number(id)} />

            <ContactForm
              companyId={Number(id)}
              contactId={isAdd ? undefined : Number(contactId)}
            />
          </div>
        </HydrationBoundary>
      </AppLayout>
    );
  } catch (error) {
    redirect('/dashboard/setup/company-contact/supplier-company');
  }
}
