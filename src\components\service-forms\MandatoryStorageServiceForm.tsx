import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  MandatoryStorageServiceSchema,
  MandatoryStorageServiceFormType,
} from '@/schema/ServiceFormSchemas';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';

interface MandatoryStorageServiceFormProps {
  onSubmit: (data: MandatoryStorageServiceFormType) => void;
  initialData?: Partial<MandatoryStorageServiceFormType>;
  isLoading?: boolean;
}

export function MandatoryStorageServiceForm({
  onSubmit,
  initialData,
  isLoading = false,
}: MandatoryStorageServiceFormProps) {
  const form = useForm<MandatoryStorageServiceFormType>({
    resolver: zodResolver(MandatoryStorageServiceSchema),
    defaultValues: {},
  });

  const handleSubmit = (data: MandatoryStorageServiceFormType) => {
    onSubmit(data);
  };

  return (
    <div className="space-y-6">
      <div className="border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          Mandatory Storage Service
        </h3>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4"
          >
            <p className="text-sm text-gray-600">
              This is a mandatory service with no additional configuration
              required.
            </p>

            <div className="flex justify-end">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save Information'}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
