import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  MandatoryStorageServiceSchema,
  MandatoryStorageServiceFormType,
} from '@/schema/ServiceFormSchemas';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';

interface MandatoryStorageServiceFormProps {
  onSubmit: (data: MandatoryStorageServiceFormType) => void;
  initialData?: Partial<MandatoryStorageServiceFormType>;
  isLoading?: boolean;
}

export function MandatoryStorageServiceForm({
  onSubmit,
  initialData,
  isLoading = false,
}: MandatoryStorageServiceFormProps) {
  const form = useForm<MandatoryStorageServiceFormType>({
    resolver: zodResolver(MandatoryStorageServiceSchema),
    defaultValues: {},
  });

  const handleSubmit = (data: MandatoryStorageServiceFormType) => {
    onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <p className="text-sm text-gray-600">
          This is a mandatory service with no additional configuration required.
        </p>

        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Saving...' : 'Save Information'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
